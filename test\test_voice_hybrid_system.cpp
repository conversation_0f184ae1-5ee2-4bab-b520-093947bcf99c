#include <unity.h>
#include "voice_hybrid_system.h"
#include <LittleFS.h>

// 测试用的模拟数据
static const uint8_t test_wav_data[] = {
    0x52, 0x49, 0x46, 0x46, 0x24, 0x08, 0x00, 0x00, 0x57, 0x41, 0x56, 0x45, 0x66, 0x6D, 0x74, 0x20,
    0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x44, 0xAC, 0x00, 0x00, 0x88, 0x58, 0x01, 0x00,
    0x02, 0x00, 0x10, 0x00, 0x64, 0x61, 0x74, 0x61, 0x00, 0x08, 0x00, 0x00
};

void setUp(void) {
    // 每个测试前的初始化
    if (!LittleFS.begin()) {
        LittleFS.format();
        LittleFS.begin();
    }
}

void tearDown(void) {
    // 每个测试后的清理
    voice_hybrid_deinit();
}

/**
 * @brief 测试语音系统初始化
 */
void test_voice_system_initialization(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试重复初始化
    result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
}

/**
 * @brief 测试ROM文件系统
 */
void test_rom_file_system(void) {
    voice_error_t result = voice_rom_init();
    // 注意：在测试环境中可能没有实际的ROM分区，所以可能返回错误
    // 这里主要测试函数调用不会崩溃
    TEST_ASSERT_TRUE(result == VOICE_ERR_OK || result == VOICE_ERR_PARTITION_NOT_FOUND);
    
    // 测试文件存在性检查
    bool exists = voice_rom_file_exists("test_file");
    TEST_ASSERT_FALSE(exists); // 测试环境中应该不存在
}

/**
 * @brief 测试压缩功能
 */
void test_compression_functions(void) {
    const uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
    uint8_t compressed_buffer[16];
    uint8_t decompressed_buffer[16];
    uint32_t compressed_size = sizeof(compressed_buffer);
    
    // 测试无压缩
    voice_error_t result = voice_compress(test_data, sizeof(test_data), 
                                         compressed_buffer, &compressed_size, 
                                         VOICE_COMPRESSION_NONE);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    TEST_ASSERT_EQUAL(sizeof(test_data), compressed_size);
    
    // 测试解压缩
    result = voice_decompress(compressed_buffer, compressed_size,
                             decompressed_buffer, sizeof(decompressed_buffer),
                             VOICE_COMPRESSION_NONE);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    TEST_ASSERT_EQUAL_MEMORY(test_data, decompressed_buffer, sizeof(test_data));
}

/**
 * @brief 测试存储管理
 */
void test_storage_management(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试存储信息获取
    voice_storage_info_t storage_info;
    result = voice_get_storage_info(&storage_info);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    TEST_ASSERT_TRUE(storage_info.littlefs_total_size > 0);
    
    // 测试存储监控
    result = voice_monitor_storage();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试存储清理
    result = voice_cleanup_storage();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
}

/**
 * @brief 测试配置功能
 */
void test_configuration_functions(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试优先级策略设置
    result = voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    result = voice_set_priority_policy(VOICE_PRIORITY_QUALITY_FIRST);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    result = voice_set_priority_policy(VOICE_PRIORITY_RELIABILITY_FIRST);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试缓存大小设置
    result = voice_set_cache_size(32);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    result = voice_set_cache_size(128);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试压缩设置
    result = voice_set_compression_enabled(true);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    result = voice_set_compression_enabled(false);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
}

/**
 * @brief 测试文件操作
 */
void test_file_operations(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 创建测试文件
    String test_file_path = "/audio/test_voice.wav";
    File test_file = LittleFS.open(test_file_path, "w");
    TEST_ASSERT_TRUE(test_file);
    
    test_file.write(test_wav_data, sizeof(test_wav_data));
    test_file.close();
    
    // 测试文件信息获取
    voice_file_info_t file_info;
    result = voice_get_file_info("test_voice", &file_info);
    // 可能返回OK或FILE_NOT_FOUND，取决于实现
    TEST_ASSERT_TRUE(result == VOICE_ERR_OK || result == VOICE_ERR_FILE_NOT_FOUND);
    
    // 清理测试文件
    LittleFS.remove(test_file_path);
}

/**
 * @brief 测试错误处理
 */
void test_error_handling(void) {
    // 测试无效参数
    voice_error_t result = voice_play(nullptr);
    TEST_ASSERT_EQUAL(VOICE_ERR_INVALID_PARAM, result);
    
    result = voice_get_file_info(nullptr, nullptr);
    TEST_ASSERT_EQUAL(VOICE_ERR_INVALID_PARAM, result);
    
    voice_file_info_t file_info;
    result = voice_get_file_info("nonexistent_file", &file_info);
    TEST_ASSERT_EQUAL(VOICE_ERR_FILE_NOT_FOUND, result);
    
    // 测试错误日志记录
    voice_log_error(VOICE_ERR_FILE_NOT_FOUND, "test_context", "test_details");
    voice_log_warning("test_context", "test_warning");
    voice_log_info("test_context", "test_info");
}

/**
 * @brief 测试缓存功能
 */
void test_cache_functions(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试缓存清理
    result = voice_clear_cache();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试预加载（可能失败，因为文件不存在）
    result = voice_preload("test_file");
    TEST_ASSERT_TRUE(result == VOICE_ERR_OK || result == VOICE_ERR_FILE_NOT_FOUND);
}

/**
 * @brief 测试版本检查
 */
void test_version_check(void) {
    voice_version_compare_result_t result = voice_check_version("test_file");
    // 在测试环境中，通常返回无效或文件未找到
    TEST_ASSERT_TRUE(result == VERSION_INVALID || result == VERSION_SAME);
}

/**
 * @brief 测试日志配置
 */
void test_logging_configuration(void) {
    // 测试日志配置
    voice_configure_logging(true, true, 50 * 1024, ESP_LOG_INFO);
    voice_configure_logging(false, true, 100 * 1024, ESP_LOG_DEBUG);
    voice_configure_logging(true, false, 200 * 1024, ESP_LOG_WARN);
    
    // 测试错误统计重置
    voice_reset_error_stats();
    
    // 测试日志清理
    voice_cleanup_logs();
}

/**
 * @brief 压力测试 - 多次初始化和反初始化
 */
void test_stress_init_deinit(void) {
    for (int i = 0; i < 10; i++) {
        voice_error_t result = voice_hybrid_init();
        TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
        
        result = voice_hybrid_deinit();
        TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    }
}

/**
 * @brief 边界条件测试
 */
void test_boundary_conditions(void) {
    voice_error_t result = voice_hybrid_init();
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试极大缓存大小
    result = voice_set_cache_size(1024); // 1MB
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试极小缓存大小
    result = voice_set_cache_size(1); // 1KB
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
    
    // 测试零缓存大小
    result = voice_set_cache_size(0);
    TEST_ASSERT_EQUAL(VOICE_ERR_OK, result);
}

/**
 * @brief 运行所有测试
 */
void runTests(void) {
    UNITY_BEGIN();
    
    RUN_TEST(test_voice_system_initialization);
    RUN_TEST(test_rom_file_system);
    RUN_TEST(test_compression_functions);
    RUN_TEST(test_storage_management);
    RUN_TEST(test_configuration_functions);
    RUN_TEST(test_file_operations);
    RUN_TEST(test_error_handling);
    RUN_TEST(test_cache_functions);
    RUN_TEST(test_version_check);
    RUN_TEST(test_logging_configuration);
    RUN_TEST(test_stress_init_deinit);
    RUN_TEST(test_boundary_conditions);
    
    UNITY_END();
}

void setup() {
    delay(2000); // 等待串口初始化
    runTests();
}

void loop() {
    // 测试完成后什么都不做
}
