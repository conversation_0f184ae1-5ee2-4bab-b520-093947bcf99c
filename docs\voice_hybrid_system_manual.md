# BLE网关语音播报混合系统使用手册

## 1. 概述

BLE网关语音播报混合系统是一个先进的语音管理解决方案，结合了Flash ROM预置语音文件和动态云端下载功能，提供快速响应、智能更新和可靠回退的语音播报体验。

### 1.1 主要特性

- **混合存储架构**：Flash ROM + LittleFS + RAM缓存三层存储
- **智能选择机制**：根据优先级策略自动选择最佳语音源
- **压缩支持**：ADPCM和μ-law压缩算法，节省存储空间
- **版本管理**：支持增量更新和版本比较
- **错误处理**：完善的错误处理和自动回退机制
- **存储管理**：自动清理和空间优化
- **日志系统**：详细的日志记录和错误统计

### 1.2 系统架构

```
语音播放请求
       ↓
   智能选择器
       ↓
┌─────────────────┐
│  优先级策略     │
│  - 速度优先     │
│  - 质量优先     │
│  - 可靠性优先   │
└─────────────────┘
       ↓
┌─────────────────┐
│  存储层次       │
│  1. RAM缓存     │
│  2. LittleFS    │
│  3. Flash ROM   │
│  4. 默认提示音  │
└─────────────────┘
       ↓
   音频播放器
```

## 2. 快速开始

### 2.1 环境准备

1. **硬件要求**
   - ESP32开发板
   - 至少4MB Flash存储
   - I2S音频输出接口

2. **软件依赖**
   - PlatformIO或Arduino IDE
   - ESP32-audioI2S库
   - ArduinoJson库
   - LittleFS库

### 2.2 安装配置

1. **复制文件**
   ```bash
   # 复制头文件
   cp include/voice_hybrid_system.h your_project/include/
   
   # 复制源文件
   cp src/voice_*.cpp your_project/src/
   
   # 复制脚本
   cp -r src/scripts your_project/src/
   
   # 复制分区表
   cp ble_gateway_partition_v2.csv your_project/
   ```

2. **更新PlatformIO配置**
   ```ini
   [env:your_env]
   board_build.partitions = ble_gateway_partition_v2.csv
   build_flags = 
       -DVOICE_HYBRID_SYSTEM_ENABLED=1
   extra_scripts = 
       pre:src/scripts/build_voice_rom.py
   ```

3. **准备语音文件**
   ```bash
   # 创建语音文件目录
   mkdir voice_files
   
   # 复制WAV文件到目录
   cp your_voice_files/*.wav voice_files/
   
   # 编辑语音文件列表
   vim voice_files/voice_list.txt
   ```

### 2.3 基本使用

```cpp
#include "voice_hybrid_system.h"

void setup() {
    // 初始化语音系统
    voice_error_t err = voice_hybrid_init();
    if (err != VOICE_ERR_OK) {
        Serial.println("语音系统初始化失败");
        return;
    }
    
    // 配置优先级策略
    voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);
    
    // 设置缓存大小
    voice_set_cache_size(64); // 64KB
    
    Serial.println("语音系统初始化完成");
}

void loop() {
    // 播放语音
    voice_play_smart("network_success");
    delay(5000);
    
    // 检查更新
    if (WiFi.status() == WL_CONNECTED) {
        voice_update_all_from_cloud();
    }
    
    // 维护任务
    voice_monitor_storage();
    delay(60000); // 1分钟后重复
}
```

## 3. API参考

### 3.1 核心函数

#### 初始化和配置

```cpp
// 初始化语音系统
voice_error_t voice_hybrid_init(void);

// 反初始化语音系统
voice_error_t voice_hybrid_deinit(void);

// 设置优先级策略
voice_error_t voice_set_priority_policy(voice_priority_policy_t policy);

// 设置缓存大小
voice_error_t voice_set_cache_size(uint32_t size_kb);

// 启用/禁用压缩
voice_error_t voice_set_compression_enabled(bool enabled);
```

#### 播放控制

```cpp
// 基本播放
voice_error_t voice_play(const char* voice_name);

// 智能播放（推荐）
voice_error_t voice_play_smart(const char* voice_name);

// 预加载到缓存
voice_error_t voice_preload(const char* voice_name);
```

#### 文件管理

```cpp
// 检查版本
voice_version_compare_result_t voice_check_version(const char* voice_name);

// 从云端更新单个文件
voice_error_t voice_update_from_cloud(const char* voice_name);

// 批量更新所有文件
voice_error_t voice_update_all_from_cloud(void);

// 获取文件信息
voice_error_t voice_get_file_info(const char* voice_name, voice_file_info_t* info);
```

#### 存储管理

```cpp
// 监控存储空间
voice_error_t voice_monitor_storage(void);

// 清理存储空间
voice_error_t voice_cleanup_storage(void);

// 获取存储信息
voice_error_t voice_get_storage_info(voice_storage_info_t* info);

// 清理缓存
voice_error_t voice_clear_cache(void);
```

### 3.2 数据结构

#### 优先级策略

```cpp
typedef enum {
    VOICE_PRIORITY_SPEED_FIRST,     // 速度优先
    VOICE_PRIORITY_QUALITY_FIRST,   // 质量优先
    VOICE_PRIORITY_RELIABILITY_FIRST // 可靠性优先
} voice_priority_policy_t;
```

#### 语音源类型

```cpp
typedef enum {
    VOICE_SOURCE_RAM_CACHE,     // RAM缓存
    VOICE_SOURCE_LITTLEFS,      // LittleFS文件系统
    VOICE_SOURCE_FLASH_ROM,     // Flash ROM
    VOICE_SOURCE_DEFAULT_TONE,  // 默认提示音
    VOICE_SOURCE_NONE           // 无可用源
} voice_source_type_t;
```

#### 文件信息

```cpp
typedef struct {
    voice_source_type_t source;       // 文件源类型
    char file_path[MAX_FILEPATH_LEN]; // 文件路径
    uint32_t file_size;               // 文件大小
    uint16_t version;                 // 文件版本
    bool is_compressed;               // 是否压缩
    uint32_t checksum;                // 校验和
} voice_file_info_t;
```

#### 存储信息

```cpp
typedef struct {
    uint32_t rom_total_size;          // ROM总大小
    uint32_t rom_used_size;           // ROM已用大小
    uint32_t littlefs_total_size;     // LittleFS总大小
    uint32_t littlefs_used_size;      // LittleFS已用大小
    uint32_t cache_total_size;        // 缓存总大小
    uint32_t cache_used_size;         // 缓存已用大小
    uint16_t rom_file_count;          // ROM文件数量
    uint16_t littlefs_file_count;     // LittleFS文件数量
} voice_storage_info_t;
```

### 3.3 错误代码

```cpp
typedef enum {
    VOICE_ERR_OK = 0,                    // 成功
    VOICE_ERR_INVALID_PARAM = -1,        // 无效参数
    VOICE_ERR_FILE_NOT_FOUND = -2,       // 文件未找到
    VOICE_ERR_DECOMPRESSION_FAILED = -3, // 解压失败
    VOICE_ERR_CHECKSUM_MISMATCH = -4,    // 校验和不匹配
    VOICE_ERR_INSUFFICIENT_MEMORY = -5,  // 内存不足
    VOICE_ERR_NETWORK_TIMEOUT = -6,      // 网络超时
    VOICE_ERR_STORAGE_FULL = -7,         // 存储空间满
    VOICE_ERR_PARTITION_NOT_FOUND = -8,  // 分区未找到
    VOICE_ERR_INVALID_HEADER = -9,       // 无效头部
    VOICE_ERR_INIT_FAILED = -10          // 初始化失败
} voice_error_t;
```

## 4. 高级功能

### 4.1 自定义优先级策略

```cpp
void setup_custom_priority() {
    // 根据应用场景选择策略
    if (battery_low) {
        // 电量低时优先可靠性
        voice_set_priority_policy(VOICE_PRIORITY_RELIABILITY_FIRST);
    } else if (network_fast) {
        // 网络快时优先质量
        voice_set_priority_policy(VOICE_PRIORITY_QUALITY_FIRST);
    } else {
        // 默认优先速度
        voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);
    }
}
```

### 4.2 动态缓存管理

```cpp
void dynamic_cache_management() {
    voice_storage_info_t info;
    voice_get_storage_info(&info);
    
    // 根据可用内存动态调整缓存大小
    uint32_t free_heap = ESP.getFreeHeap();
    uint32_t cache_size = min(free_heap / 4, 128); // 最多使用1/4内存，不超过128KB
    
    voice_set_cache_size(cache_size);
}
```

### 4.3 错误处理和恢复

```cpp
void handle_voice_error(voice_error_t error, const char* voice_name) {
    switch (error) {
        case VOICE_ERR_NETWORK_TIMEOUT:
            // 网络超时，稍后重试
            voice_log_warning("network", "Timeout, will retry later");
            break;
            
        case VOICE_ERR_STORAGE_FULL:
            // 存储满，清理空间
            voice_cleanup_storage();
            break;
            
        case VOICE_ERR_FILE_NOT_FOUND:
            // 文件未找到，尝试下载
            voice_update_from_cloud(voice_name);
            break;
            
        default:
            voice_log_error(error, "playback", voice_name);
            break;
    }
}
```

## 5. 故障排除

### 5.1 常见问题

**Q: 语音播放没有声音**
A: 检查以下项目：
- 音频输出硬件连接
- audio_enable_flag是否为1
- 语音文件是否存在
- I2S配置是否正确

**Q: 语音文件下载失败**
A: 检查：
- 网络连接状态
- 云端服务器可访问性
- 存储空间是否充足
- SSL证书配置

**Q: 存储空间不足**
A: 解决方案：
- 调用voice_cleanup_storage()清理
- 减少缓存大小
- 启用压缩功能
- 删除不必要的语音文件

### 5.2 调试技巧

1. **启用详细日志**
   ```cpp
   voice_configure_logging(true, true, 100*1024, ESP_LOG_DEBUG);
   ```

2. **监控存储状态**
   ```cpp
   voice_storage_info_t info;
   voice_get_storage_stats(&info);
   // 打印存储信息
   ```

3. **检查错误统计**
   ```cpp
   String error_log = voice_export_error_log();
   Serial.println(error_log);
   ```

## 6. 性能优化

### 6.1 内存优化

- 合理设置缓存大小
- 及时清理不用的文件
- 启用压缩功能

### 6.2 速度优化

- 预加载常用语音文件
- 使用速度优先策略
- 优化网络配置

### 6.3 可靠性优化

- 使用可靠性优先策略
- 定期备份重要语音文件
- 监控系统健康状态

## 7. 更新日志

### v1.0.0 (2024-06-24)
- 初始版本发布
- 支持混合存储架构
- 实现智能选择机制
- 添加压缩支持
- 完善错误处理系统
