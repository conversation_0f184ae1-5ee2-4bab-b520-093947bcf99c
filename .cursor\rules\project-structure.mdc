---
description: 
globs: 
alwaysApply: true
---
# 项目结构规则

这个项目是一个基于ESP32的蓝牙网关设备固件。主要功能包括:

- 蓝牙设备数据采集
- WiFi网络连接
- MQTT服务器通信
- 本地数据存储
- UI界面显示

## 核心文件

主要入口文件是 [src/main.cpp](mdc:src/main.cpp),包含了所有核心功能的实现。

## 项目结构说明

- `/src` - 源代码目录
  - `main.cpp` - 主程序入口
  - `main.h` - 主要头文件
  - `main_include.h` - 包含所有依赖头文件
  - `/ble_server` - 蓝牙服务相关代码
  - `/lvgl` - UI界面相关代码
  - `/audio` - 音频处理相关代码

- `/data` - 数据存储目录
  - `/config` - 配置文件
  - `/img` - 图片资源
  - `/last` - 最近数据
  - `/audio` - 音频文件

## 关键模块

1. 蓝牙模块 - 负责扫描和连接蓝牙设备,获取数据
2. WiFi模块 - 处理网络连接和MQTT通信
3. 存储模块 - 管理本地文件系统和数据存储
4. UI模块 - 使用LVGL库实现用户界面
5. 音频模块 - 处理语音提示功能

