/*
 * Copyright 2024 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"

static S_WifiConnectInfo info;

void setup_scr_WifiConnect(struct page *page)
{
	if (info.name == NULL)
	{
		info.name = (char *)ps_malloc(strlen("none") + 1);
		strcpy(info.name, "none");
	}
	// Write codes WifiConnect
	WifiConnect = page->body_obj;

	// Write codes WifiConnect_Page
	WifiConnect_Page = lv_obj_create(WifiConnect);
	lv_obj_set_pos(WifiConnect_Page, 0, 30);
	lv_obj_set_size(WifiConnect_Page, 320, 210);
	lv_obj_set_scrollbar_mode(WifiConnect_Page, LV_SCROLLBAR_MODE_OFF);

	// Write style for WifiConnect_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiConnect_Page, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(WifiConnect_Page, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_PageInfoCont
	WifiConnect_PageInfoCont = lv_obj_create(WifiConnect_Page);
	lv_obj_set_pos(WifiConnect_PageInfoCont, 10, 15);
	lv_obj_set_size(WifiConnect_PageInfoCont, 300, 52);
	lv_obj_set_scrollbar_mode(WifiConnect_PageInfoCont, LV_SCROLLBAR_MODE_OFF);

	// Write style for WifiConnect_PageInfoCont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_PageInfoCont, 9, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiConnect_PageInfoCont, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(WifiConnect_PageInfoCont, lv_color_hex(0xececec), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_PageInfoCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_InfoConnectBtn
	WifiConnect_InfoConnectBtn = lv_btn_create(WifiConnect_PageInfoCont);
	WifiConnect_InfoConnectBtn_label = lv_label_create(WifiConnect_InfoConnectBtn);
	lv_label_set_text(WifiConnect_InfoConnectBtn_label, "Connect");
	lv_label_set_long_mode(WifiConnect_InfoConnectBtn_label, LV_LABEL_LONG_WRAP);
	lv_obj_align(WifiConnect_InfoConnectBtn_label, LV_ALIGN_CENTER, 0, 0);
	lv_obj_set_style_pad_all(WifiConnect_InfoConnectBtn, 0, LV_STATE_DEFAULT);
	lv_obj_align(WifiConnect_InfoConnectBtn, LV_ALIGN_RIGHT_MID, -10, 0);
	lv_obj_set_size(WifiConnect_InfoConnectBtn, 73, 28);

	// Write style for WifiConnect_InfoConnectBtn, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_bg_opa(WifiConnect_InfoConnectBtn, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(WifiConnect_InfoConnectBtn, lv_color_hex(EMPHASIS_COLOR), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(WifiConnect_InfoConnectBtn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_InfoConnectBtn, 14, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_InfoConnectBtn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiConnect_InfoConnectBtn, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiConnect_InfoConnectBtn, &lv_font_sf_compact_m_14, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiConnect_InfoConnectBtn, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_InfoWifiName
	WifiConnect_InfoWifiName = lv_label_create(WifiConnect_PageInfoCont);
	lv_label_set_text(WifiConnect_InfoWifiName, info.name);
	lv_label_set_long_mode(WifiConnect_InfoWifiName, LV_LABEL_LONG_WRAP);
	lv_obj_align(WifiConnect_InfoWifiName, LV_ALIGN_LEFT_MID, lv_pct(15), 0);
	lv_obj_set_size(WifiConnect_InfoWifiName, 164, 17);

	// Write style for WifiConnect_InfoWifiName, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiConnect_InfoWifiName, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiConnect_InfoWifiName, &lv_font_sf_compact_m_14, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_letter_space(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_line_space(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiConnect_InfoWifiName, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_InfoWifiName, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_InfoWifiIcon
	WifiConnect_InfoWifiIcon = lv_label_create(WifiConnect_PageInfoCont);
	lv_obj_align(WifiConnect_InfoWifiIcon, LV_ALIGN_LEFT_MID, lv_pct(5), 0);
	lv_obj_set_size(WifiConnect_InfoWifiIcon, 16, 16);

	// Write style for WifiConnect_InfoWifiIcon, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	if (info.wifi == 0)
	{
		lv_label_set_text(WifiConnect_InfoWifiIcon, WIFI_OFF_ICON);
		lv_obj_clear_flag(WifiConnect_InfoConnectBtn, LV_OBJ_FLAG_HIDDEN);
		lv_obj_set_style_text_color(WifiConnect_InfoWifiIcon, lv_color_hex(0x999999), 0);
	}
	else if (info.wifi == 1)
	{
		lv_label_set_text(WifiConnect_InfoWifiIcon, WIFI_ICON);
		lv_obj_add_flag(WifiConnect_InfoConnectBtn, LV_OBJ_FLAG_HIDDEN);
		lv_obj_set_style_text_color(WifiConnect_InfoWifiIcon, lv_color_hex(EMPHASIS_COLOR), 0);
	}
	lv_obj_set_style_text_font(WifiConnect_InfoWifiIcon, &lv_font_iconfont_16, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_PageConfigBtn
	WifiConnect_PageConfigBtn = lv_btn_create(WifiConnect_Page);
	WifiConnect_PageConfigBtn_label = lv_label_create(WifiConnect_PageConfigBtn);
	lv_label_set_text(WifiConnect_PageConfigBtn_label, "Smart Config");
	lv_label_set_long_mode(WifiConnect_PageConfigBtn_label, LV_LABEL_LONG_WRAP);
	lv_obj_align(WifiConnect_PageConfigBtn_label, LV_ALIGN_CENTER, 0, 0);
	lv_obj_set_style_pad_all(WifiConnect_PageConfigBtn, 0, LV_STATE_DEFAULT);
	lv_obj_set_pos(WifiConnect_PageConfigBtn, 30, 97);
	lv_obj_set_size(WifiConnect_PageConfigBtn, 258, 40);

	// Write style for WifiConnect_PageConfigBtn, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_bg_opa(WifiConnect_PageConfigBtn, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(WifiConnect_PageConfigBtn, lv_color_hex(0xececec), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(WifiConnect_PageConfigBtn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_PageConfigBtn, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_PageConfigBtn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiConnect_PageConfigBtn, lv_color_hex(EMPHASIS_COLOR), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiConnect_PageConfigBtn, &lv_font_sf_compact_m_14, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiConnect_PageConfigBtn, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiConnect_PageDetailText
	WifiConnect_PageDetailText = lv_label_create(WifiConnect_Page);
	lv_label_set_text(WifiConnect_PageDetailText, "Tap \"Smart Config\" to configure Wi-Fi network via your smartphone");
	lv_label_set_long_mode(WifiConnect_PageDetailText, LV_LABEL_LONG_WRAP);
	lv_obj_set_pos(WifiConnect_PageDetailText, 17, 150);
	lv_obj_set_size(WifiConnect_PageDetailText, 286, 39);

	// Write style for WifiConnect_PageDetailText, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiConnect_PageDetailText, lv_color_hex(0x888888), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiConnect_PageDetailText, &lv_font_sf_compact_m_15, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_letter_space(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_line_space(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiConnect_PageDetailText, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiConnect_PageDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// // Update current screen layout.
	// lv_obj_update_layout(WifiConnect);

	lv_group_add_obj(group, WifiConnect_InfoConnectBtn);
	lv_group_add_obj(group, WifiConnect_PageConfigBtn);

	events_init_WifiConnect();

	lv_group_focus_obj(WifiConnect_PageConfigBtn);

	bar_on("Wi-Fi");

	// lv_scr_load_anim(page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
}

void exit_scr_WifiConnect(struct page *old_page, struct page *new_page)
{
	lv_group_remove_obj(WifiConnect_InfoConnectBtn);
	lv_group_remove_obj(WifiConnect_PageConfigBtn);
	bar_off();
	lv_obj_clean(old_page->body_obj);
	// lv_scr_load_anim(new_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
}

void info_modify_WifiConnect(S_WifiConnectInfo info_data)
{
	if (info_data.name != NULL)
	{
		if (info.name != NULL)
		{
			free(info.name);
			info.name = NULL;
		}
		info.name = (char *)ps_malloc(strlen(info_data.name) + 1);
		strcpy(info.name, info_data.name);
	}

	info.wifi = info_data.wifi;

	// 更新当前页面的控件
	if (get_cur_page()->body_obj == WifiConnect)
	{
		lv_label_set_text(WifiConnect_InfoWifiName, info.name);
		if (info.wifi == 0)
		{
			lv_label_set_text(WifiConnect_InfoWifiIcon, WIFI_OFF_ICON);
			lv_obj_clear_flag(WifiConnect_InfoConnectBtn, LV_OBJ_FLAG_HIDDEN);
			lv_obj_set_style_text_color(WifiConnect_InfoWifiIcon, lv_color_hex(0x999999), 0);
		}
		else if (info.wifi == 1)
		{
			lv_label_set_text(WifiConnect_InfoWifiIcon, WIFI_ICON);
			lv_obj_add_flag(WifiConnect_InfoConnectBtn, LV_OBJ_FLAG_HIDDEN);
			lv_obj_set_style_text_color(WifiConnect_InfoWifiIcon, lv_color_hex(EMPHASIS_COLOR), 0);
		}
	}
}

void refresh_scr_WifiConnect(void)
{
	lv_group_remove_all_objs(group);
	lv_group_add_obj(group, WifiConnect_InfoConnectBtn);
	lv_group_add_obj(group, WifiConnect_PageConfigBtn);

	events_init_WifiConnect();

	lv_group_focus_obj(WifiConnect_PageConfigBtn);
}
