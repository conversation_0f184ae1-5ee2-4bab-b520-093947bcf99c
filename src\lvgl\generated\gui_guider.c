/*
 * Copyright 2024 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"

#define LOAD_STATUSBAR(bar_name)                     \
	do                                               \
	{                                                \
		strcpy(status_bar.name, #bar_name);          \
		status_bar.body_obj = create_new_bar();      \
		status_bar.onCreate = setup_scr_##bar_name;  \
		status_bar.onStart = start_scr_##bar_name;   \
		status_bar.onFresh = refresh_scr_##bar_name; \
		status_bar.onExit = exit_scr_##bar_name;     \
		status_bar.onRename = rename_scr_##bar_name; \
	} while (0)

#define LOAD_STATUSBAR_VITALS(bar_name)                    \
	do                                                     \
	{                                                      \
		strcpy(sta_bar_vitals.name, #bar_name);            \
		sta_bar_vitals.body_obj = create_new_vitals_bar(); \
		sta_bar_vitals.onCreate = setup_scr_##bar_name;    \
		sta_bar_vitals.onStart = start_scr_##bar_name;     \
		sta_bar_vitals.onExit = exit_scr_##bar_name;       \
	} while (0)

#define LOAD_SCR_PAGE(page_name, level)                          \
	do                                                           \
	{                                                            \
		strcpy(page_admin[page_num++].name, #page_name);         \
		page_admin[page_num - 1].body_obj = create_new_screen(); \
		page_admin[page_num - 1].page_level = level;             \
		page_admin[page_num - 1].onInit = setup_scr_##page_name; \
		page_admin[page_num - 1].onExit = exit_scr_##page_name;  \
		lv_scr_load(page_admin[page_num - 1].body_obj);          \
	} while (0)

#define LOAD_POP(pop_name)                                      \
	do                                                          \
	{                                                           \
		strcpy(pop_admin[pop_num++].name, #pop_name);           \
		pop_admin[pop_num - 1].body_obj = create_new_pop();     \
		pop_admin[pop_num - 1].onCreate = setup_scr_##pop_name; \
		pop_admin[pop_num - 1].onStart = start_scr_##pop_name;  \
		pop_admin[pop_num - 1].onExit = exit_scr_##pop_name;    \
	} while (0)

typedef struct
{
	char name[10][32];
	int top;
} page_stack;

page_stack *create_page_stack(void)
{
	page_stack *stack = (page_stack *)ps_malloc(sizeof(page_stack));
	stack->top = 0;
	return stack;
}

// 释放栈空间
void free_page_stack(page_stack *stack)
{
	if (stack != NULL)
	{
		ui_log("stack top before free:%d", stack->top);
		// 先记录 top 值并输出日志，然后再释放内存
		int top = stack->top;
		free(stack);
		ui_log("stack freed, top was:%d", top);
	}
	else
	{
		ui_log("attempted to free NULL stack");
	}
}

void page_push(page_stack *stack, char *name)
{
	if (stack->top >= 10)
	{
		ui_log("page stack overflow");
		return;
	}
	strcpy(stack->name[stack->top++], name);
	ui_log("stack top :%d", stack->top);
}

char *page_pop(page_stack *stack)
{
	if (stack->top <= 0)
	{
		ui_log("page stack underflow");
		return NULL;
	}
	return stack->name[--stack->top];
}

void stack_print(page_stack *stack)
{
	for (int i = 0; i < stack->top; i++)
	{
		ui_log("stack %d : %s", i, stack->name[i]);
	}
}

page_stack *stack;

static struct sta_bar status_bar;
static struct sta_bar_vitals sta_bar_vitals;

static struct page page_admin[30];
static int page_num = 0;
struct page *cur_page;

static struct pop pop_admin[10];
static int pop_num = 0;
struct pop *cur_pop;

lv_style_t page_style;

lv_obj_t *StatusBar;
lv_obj_t *StatusBarBattery;
lv_obj_t *StatusBarTime;
lv_obj_t *StatusBarWifi;
lv_obj_t *StatusBarName;
lv_obj_t *StatusBarVitals;
lv_obj_t *StatusBarVitals_Label1;
lv_obj_t *StatusBarVitals_Label2;
lv_obj_t *StatusBarVitals_Label3;
lv_obj_t *StatusBarVitals_Label4;
lv_obj_t *StatusBarVitals_Label5;
lv_obj_t *Home;
bool Home_del;
lv_obj_t *Home_Page;
lv_obj_t *Home_PageImg;
lv_obj_t *Home_PageDetailText;
lv_obj_t *WifiConnect;
bool WifiConnect_del;
lv_obj_t *WifiConnect_Page;
lv_obj_t *WifiConnect_PageInfoCont;
lv_obj_t *WifiConnect_InfoConnectBtn;
lv_obj_t *WifiConnect_InfoConnectBtn_label;
lv_obj_t *WifiConnect_InfoWifiName;
lv_obj_t *WifiConnect_InfoWifiIcon;
lv_obj_t *WifiConnect_PageConfigBtn;
lv_obj_t *WifiConnect_PageConfigBtn_label;
lv_obj_t *WifiConnect_PageDetailText;
lv_obj_t *WifiConnectLv1;
bool WifiConnectLv1_del;
lv_obj_t *WifiConnectLv1_Page;
lv_obj_t *WifiConnectLv1_PageInfoCont;
lv_obj_t *WifiConnectLv1_InfoConnectBtn;
lv_obj_t *WifiConnectLv1_InfoConnectBtn_label;
lv_obj_t *WifiConnectLv1_InfoWifiName;
lv_obj_t *WifiConnectLv1_InfoWifiIcon;
lv_obj_t *WifiConnectLv1_PageConfigBtn;
lv_obj_t *WifiConnectLv1_PageConfigBtn_label;
lv_obj_t *WifiConnectLv1_PageDetailText;
lv_obj_t *Family;
bool Family_del;
lv_obj_t *Family_BindingPage;
lv_obj_t *Family_PageDetailText;
lv_obj_t *Family_PageMemberCont;
lv_obj_t *Family_NoBindingPage;
lv_obj_t *Family_NoBindingText;
lv_obj_t *Family_NoBindingImg;
lv_obj_t *Patient;
bool Patient_del;
lv_obj_t *Patient_Page;
lv_obj_t *Patient_PageBtnCont;
lv_obj_t *Patient_ContSetting;
lv_obj_t *Patient_SettingStr;
lv_obj_t *Patient_SettingIcon;
lv_obj_t *Patient_ContVitals;
lv_obj_t *Patient_VitalsStr;
lv_obj_t *Patient_VitalsIcon;
lv_obj_t *Patient_ContMessage;
lv_obj_t *Patient_MessageStr;
lv_obj_t *Patient_MessageIcon;
lv_obj_t *Patient_PageMemberInfo;
lv_obj_t *Patient_InfoIcon;
lv_obj_t *Patient_IconStr;
lv_obj_t *Patient_InfoName;
lv_obj_t *Oxygen;
bool Oxygen_del;
lv_obj_t *Oxygen_Page;
lv_obj_t *Oxygen_PageDetectTime;
lv_obj_t *Oxygen_PageCont;
lv_obj_t *Oxygen_ContPR;
lv_obj_t *Oxygen_PRValue;
lv_obj_t *Oxygen_PRStr;
lv_obj_t *Oxygen_ContPI;
lv_obj_t *Oxygen_PIStr;
lv_obj_t *Oxygen_PIValue;
lv_obj_t *Oxygen_ContSO2;
lv_obj_t *Oxygen_SO2Value;
lv_obj_t *Oxygen_SO2Str;
lv_obj_t *Oxygen_PageRightBtn;
lv_obj_t *Oxygen_PageRightBtn_label;
lv_obj_t *Pressure;
bool Pressure_del;
lv_obj_t *Pressure_Page;
lv_obj_t *Pressure_PageDetectTime;
lv_obj_t *Pressure_PageLeftBtn;
lv_obj_t *Pressure_PageLeftBtn_label;
lv_obj_t *Pressure_PageRightBtn;
lv_obj_t *Pressure_PageRightBtn_label;
lv_obj_t *Pressure_PageCont;
lv_obj_t *Pressure_ContPUL;
lv_obj_t *Pressure_PULValue;
lv_obj_t *Pressure_PULStr;
lv_obj_t *Pressure_ContDIA;
lv_obj_t *Pressure_DIAValue;
lv_obj_t *Pressure_DIAStr;
lv_obj_t *Pressure_ContSYS;
lv_obj_t *Pressure_SYSValue;
lv_obj_t *Pressure_SYSStr;
lv_obj_t *Weight;
bool Weight_del;
lv_obj_t *Weight_Page;
lv_obj_t *Weight_PageDetectTime;
lv_obj_t *Weight_PageCont;
lv_obj_t *Weight_ContWeight;
lv_obj_t *Weight_WeightUnit;
lv_obj_t *Weight_WeightValue;
lv_obj_t *Weight_PageLeftBtn;
lv_obj_t *Weight_PageLeftBtn_label;
lv_obj_t *Weight_PageRightBtn;
lv_obj_t *Weight_PageRightBtn_label;
lv_obj_t *Temperature;
bool Temperature_del;
lv_obj_t *Temperature_Page;
lv_obj_t *Temperature_PageDetectTime;
lv_obj_t *Temperature_PageCont;
lv_obj_t *Temperature_ContTemperature;
lv_obj_t *Temperature_TemperUnit;
lv_obj_t *Temperature_TemperValue;
lv_obj_t *Temperature_PageLeftBtn;
lv_obj_t *Temperature_PageLeftBtn_label;
lv_obj_t *Temperature_PageRightBtn;
lv_obj_t *Temperature_PageRightBtn_label;
lv_obj_t *BloodGlucose;
bool BloodGlucose_del;
lv_obj_t *BloodGlucose_Page;
lv_obj_t *BloodGlucose_PageDetectTime;
lv_obj_t *BloodGlucose_PageCont;
lv_obj_t *BloodGlucose_ContBloodGlucose;
lv_obj_t *BloodGlucose_BloodGlucoseUnit;
lv_obj_t *BloodGlucose_BloodGlucoseValue;
lv_obj_t *BloodGlucose_ContDataMealTime;
lv_obj_t *BloodGlucose_ContDataMealTime_label;
lv_obj_t *BloodGlucose_PageLeftBtn;
lv_obj_t *BloodGlucose_PageLeftBtn_label;
lv_obj_t *Vitals;
bool Vitals_del;
lv_obj_t *Vitals_DataPage;
lv_obj_t *Vitals_NoDataPage;
lv_obj_t *Vitals_NoDataPageDetailText;
lv_obj_t *Vitals_NoDataPageIcon;
lv_obj_t *WifiComplete;
bool WifiComplete_del;
lv_obj_t *WifiComplete_WifiPage;
lv_obj_t *WifiComplete_PageCont;
lv_obj_t *WifiComplete_ContDetailText;
lv_obj_t *WifiComplete_ContIcon;
lv_obj_t *DataDisplay;
bool DataDisplay_del;
lv_obj_t *DataDisplay_PageCont;
lv_obj_t *DataDisplay_ContTopicLab;
lv_obj_t *DataDisplay_ContDetailData;
lv_obj_t *DataDisplay_ContMember;
lv_obj_t *DataDisplay_ContTime;
lv_obj_t *GlucoseDisplay;
bool GlucoseDisplay_del;
lv_obj_t *GlucoseDisplay_PageCont;
lv_obj_t *GlucoseDisplay_ContDetailData;
lv_obj_t *GlucoseDisplay_DataBeforeMeal;
lv_obj_t *GlucoseDisplay_DataBeforeMeal_label;
lv_obj_t *GlucoseDisplay_DataAfterMeal;
lv_obj_t *GlucoseDisplay_DataAfterMeal_label;
lv_obj_t *GlucoseDisplay_SpanGroup;
lv_span_t *GlucoseDisplay_SpanGroup_Span;
lv_obj_t *GlucoseDisplay_ContMember;
lv_obj_t *GlucoseDisplay_ContTime;
lv_obj_t *Setting;
bool Setting_del;
lv_obj_t *Setting_Page;
lv_obj_t *Setting_PageCont;
lv_obj_t *Setting_ContAbout;
lv_obj_t *Setting_AboutStr;
lv_obj_t *Setting_AboutIcon;
lv_obj_t *Setting_ContVoice;
lv_obj_t *Setting_VoiceStr;
lv_obj_t *Setting_VoiceIcon;
lv_obj_t *Setting_ContSleep;
lv_obj_t *Setting_SleepStr;
lv_obj_t *Setting_SleepIcon;
lv_obj_t *Setting_ContRecovery;
lv_obj_t *Setting_RecoveryStr;
lv_obj_t *Setting_RecoveryIcon;
lv_obj_t *Setting_ContWifi;
lv_obj_t *Setting_WifiStr;
lv_obj_t *Setting_WifiIcon;
lv_obj_t *Voice;
bool Voice_del;
lv_obj_t *Voice_Page;
lv_obj_t *Voice_PageCont;
lv_obj_t *Voice_ContStr;
lv_obj_t *Voice_ContSW;
lv_obj_t *Voice_PageDetailText;
lv_obj_t *Version;
bool Version_del;
lv_obj_t *Version_Page;
lv_obj_t *Version_PageDeviceName;
lv_obj_t *Version_PageSerStr;
lv_obj_t *Version_PageCurStr;
lv_obj_t *Version_PageBtn;
lv_obj_t *Version_PageBtn_label;
lv_obj_t *MessageRead;
bool MessageRead_del;
lv_obj_t *MessageRead_Page;
lv_obj_t *MessageRead_PageMsgTitle;
lv_obj_t *MessageRead_PageMsg;
lv_obj_t *MessageRead_PageBtn;
lv_obj_t *MessageRead_PageBtn_label;
lv_obj_t *NewVersion;
bool NewVersion_del;
lv_obj_t *NewVersion_Page;
lv_obj_t *NewVersion_PageVersionTitle;
lv_obj_t *NewVersion_PageVerisonMsg;
lv_obj_t *NewVersion_PageBtn;
lv_obj_t *NewVersion_PageBtn_label;
lv_obj_t *Updating;
bool Updating_del;
lv_obj_t *Updating_Page;
lv_obj_t *Updating_PageDetailText;
lv_obj_t *Updating_PageProcessBar;
lv_obj_t *Updating_PageProcess;
lv_obj_t *Message;
bool Message_del;
lv_obj_t *Message_Page;
lv_obj_t *Message_NoMsgPageCont;
lv_obj_t *Message_PageDetailText;
lv_obj_t *Message_PageImg;
lv_obj_t *Message_PageCont;
lv_obj_t *WifiOperation;
bool WifiOperation_del;
lv_obj_t *WifiOperation_Page;
lv_obj_t *WifiOperation_PageCont;
lv_obj_t *WifiOperation_ContDetailText;
lv_obj_t *WifiOperation_ContImg;

lv_obj_t *WifiConnecting;
bool WifiConnecting_del;
lv_obj_t *WifiConnecting_Cont;
lv_obj_t *WifiConnecting_ContGif;
lv_obj_t *WifiConnecting_ContDetailText;
lv_obj_t *WifiFailed;
bool WifiFailed_del;
lv_obj_t *WifiFailed_Cont;
lv_obj_t *WifiFailed_ContDetailText;
lv_obj_t *WifiFailed_Btn;
lv_obj_t *WifiFailed_Btn_Label;
lv_obj_t *WifiFailed_ContImg;

lv_obj_t *WifiConnectingFull;
bool WifiConnectingFull_del;
lv_obj_t *WifiConnectingFull_Page;
lv_obj_t *WifiConnectingFull_ContImg;
lv_obj_t *WifiConnectingFull_Gif;
lv_obj_t *WifiConnectingFull_DetailText;

lv_obj_t *WifiFailedFull;
bool WifiFailedFull_del;
lv_obj_t *WifiFailedFull_Page;
lv_obj_t *WifiFailedFull_ContDetailText;
lv_obj_t *WifiFailedFull_Btn;
lv_obj_t *WifiFailedFull_Btn_Label;
lv_obj_t *WifiFailedFull_ContImg;

lv_obj_t *Recovery;
bool Recovery_del;
lv_obj_t *Recovery_Cont;
lv_obj_t *Recovery_ContDetailText;
lv_obj_t *Recovery_Btn;
lv_obj_t *Recovery_Btn_Label;
lv_obj_t *Recovery_ContImg;

lv_obj_t *SleepScreen;
bool SleepScreen_del;
lv_obj_t *SleepScreen_Page;
lv_obj_t *SleepScreen_PageCont;
lv_obj_t *SleepScreen_Cont1;
lv_obj_t *SleepScreen_Cont1Lable;
lv_obj_t *SleepScreen_Cont1Icon;
lv_obj_t *SleepScreen_Cont2;
lv_obj_t *SleepScreen_Cont2Lable;
lv_obj_t *SleepScreen_Cont2Icon;
lv_obj_t *SleepScreen_Cont3;
lv_obj_t *SleepScreen_Cont3Lable;
lv_obj_t *SleepScreen_Cont3Icon;
lv_obj_t *SleepScreen_Cont4;
lv_obj_t *SleepScreen_Cont4Lable;
lv_obj_t *SleepScreen_Cont4Icon;

lv_obj_t *create_new_bar(void)
{
	lv_obj_t *main_obj = lv_obj_create(lv_layer_top());
	lv_obj_clean(main_obj);
	lv_obj_set_pos(main_obj, 0, 0);
	lv_obj_set_size(main_obj, 320, 30);

	return main_obj;
}

lv_obj_t *create_new_vitals_bar(void)
{
	lv_obj_t *main_obj = lv_obj_create(lv_layer_top());
	lv_obj_clean(main_obj);
	lv_obj_set_pos(main_obj, 0, -30);
	lv_obj_set_size(main_obj, 320, 30);

	return main_obj;
}

lv_obj_t *create_new_screen(void)
{
	lv_obj_t *main_obj = lv_obj_create(NULL);
	lv_obj_clean(main_obj);
	lv_obj_set_size(main_obj, 320, 240); // 根据个人屏幕大小修改
	lv_obj_add_style(main_obj, &page_style, 0);

	return main_obj;
}

lv_obj_t *create_new_pop(void)
{
	lv_obj_t *main_obj = lv_obj_create(lv_layer_sys());
	lv_obj_set_pos(main_obj, 20, -20);
	lv_obj_set_size(main_obj, 20, 20);

	return main_obj;
}
struct page *get_cur_page(void)
{
	return cur_page;
}
struct pop *get_cur_pop(void)
{
	return cur_pop;
}

void clean_stack(void)
{
	stack->top = 0;
}

void lv_anim_timeline_add_wrapper(lv_anim_timeline_t *at, const lv_anim_timeline_wrapper_t *wrapper)
{
	for (uint32_t i = 0; wrapper[i].obj != NULL; i++)
	{
		const lv_anim_timeline_wrapper_t *atw = &wrapper[i];

		lv_anim_t a;
		lv_anim_init(&a);
		lv_anim_set_var(&a, atw->obj);
		lv_anim_set_values(&a, atw->start, atw->end);
		lv_anim_set_exec_cb(&a, atw->exec_cb);
		lv_anim_set_time(&a, atw->duration);
		lv_anim_set_path_cb(&a, atw->path_cb);
		lv_anim_set_early_apply(&a, atw->early_apply);

		lv_anim_timeline_add(at, atw->start_time, &a);
	}
}

void ui_init_style(lv_style_t *style)
{
	if (style->prop_cnt > 1)
		lv_style_reset(style);
	else
		lv_style_init(style);
}

void init_scr_del_flag(void)
{
	Home_del = true;
	WifiConnect_del = true;
	Family_del = true;
	Patient_del = true;
	Oxygen_del = true;
	Pressure_del = true;
	Weight_del = true;
	Temperature_del = true;
	Vitals_del = true;
	WifiComplete_del = true;
	DataDisplay_del = true;
	Setting_del = true;
	Voice_del = true;
	Version_del = true;
	MessageRead_del = true;
	NewVersion_del = true;
	Updating_del = true;
	Message_del = true;
	SleepScreen_del = true;
	WifiConnecting_del = true;
	WifiFailed_del = true;
	WifiConnectingFull_del = true;
	WifiFailedFull_del = true;
	Recovery_del = true;
	GlucoseDisplay_del = true;
}

void re_create_page_stack(void)
{
	// 检查是否已经存在栈，如果存在则先释放
	if (stack != NULL)
	{
		free_page_stack(stack);
	}

	stack = create_page_stack();
	if (stack != NULL)
	{
		ui_log("stack recreated, top:%d", stack->top);
	}
	else
	{
		ui_log("failed to recreate stack");
	}
}

void delete_page_stack(void)
{
	if (stack != NULL)
	{
		free_page_stack(stack);
		stack = NULL; // 重要：将指针设为NULL，避免悬挂指针
		ui_log("page stack deleted and set to NULL");
	}
	else
	{
		ui_log("attempted to delete NULL page stack");
	}
}

void setup_ui(void)
{
	init_scr_del_flag();
	stack = create_page_stack();

	// 加载状态栏
	LOAD_STATUSBAR(StatusBar);
	LOAD_STATUSBAR_VITALS(StatusBarVitals);

	status_bar.onCreate(&status_bar, "FondCircle");
	status_bar.onStart();

	// 加载主页并将主页设置为显示页面
	LOAD_SCR_PAGE(Home, 0);
	cur_page = &page_admin[page_num - 1];

	// 加载其他页面
	LOAD_SCR_PAGE(WifiConnectingFull, 1);
	LOAD_SCR_PAGE(WifiFailedFull, 1);
	LOAD_SCR_PAGE(WifiConnectLv1, 1);
	LOAD_SCR_PAGE(Family, 2);
	LOAD_SCR_PAGE(Patient, 3);
	LOAD_SCR_PAGE(Vitals, 4);
	LOAD_SCR_PAGE(Settings, 4);
	LOAD_SCR_PAGE(Voice, 5);
	LOAD_SCR_PAGE(Version, 5);
	LOAD_SCR_PAGE(WifiConnect, 5);
	LOAD_SCR_PAGE(SleepScreen, 5);
	LOAD_SCR_PAGE(Messages, 4);
	LOAD_SCR_PAGE(MessageRead, 5);
	LOAD_SCR_PAGE(NewVersion, 5);
	LOAD_SCR_PAGE(Updating, 6);

	// 加载弹窗页面
	LOAD_POP(WifiOperation);
	LOAD_POP(WifiComplete);
	LOAD_POP(DataDisplay);
	LOAD_POP(WifiConnecting);
	LOAD_POP(WifiFailed);
	LOAD_POP(Recovery);
	LOAD_POP(GlucoseDisplay);

	cur_page->onInit(cur_page);
}
// 搜索特定页面
static struct page *page_search(const char *name)
{
	for (int i = 0; i < page_num; i++)
	{
		if (strcmp(page_admin[i].name, name) == 0)
		{
			return &page_admin[i];
		}
	}
	return NULL;
}
// 页面切换
int page_transition(const char *new_page_name)
{
	// 检查参数是否为空
	if (new_page_name == NULL)
	{
		ui_log("page_transition: NULL page name");
		return -1;
	}

	ui_log("%s", new_page_name);
	if (strcmp(new_page_name, "") == 0)
	{
		ui_log("NO PAGE");
		return -1;
	}

	struct page *new_page = page_search(new_page_name);
	if (new_page != NULL)
	{
		// 检查当前页面是否有效
		if (cur_page == NULL)
		{
			ui_log("page_transition: Current page is NULL");
			cur_page = new_page;
			new_page->onInit(new_page);
			lv_scr_load_anim(new_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
			return 0;
		}

		// 检查退出回调
		if (cur_page->onExit == NULL)
		{
			ui_log("page_transition: Current page has NULL exit callback");
			cur_page = new_page;
			new_page->onInit(new_page);
			lv_scr_load_anim(new_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
			return 0;
		}

		if (stack != NULL && new_page->page_level > cur_page->page_level)
		{
			ui_log("push page %s", new_page_name);
			page_push(stack, cur_page->name);
		}

		// 安全调用退出回调
		ui_log("exit old page:%s", cur_page->name);
		cur_page->onExit(cur_page, new_page);

		cur_page = new_page;

		// 检查初始化回调
		if (new_page->onInit != NULL)
		{
			new_page->onInit(new_page);
		}
		else
		{
			ui_log("page_transition: New page has NULL init callback");
		}

		lv_scr_load_anim(new_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
		ui_log("page transition success");
		return 0;
	}
	else
	{
		ui_log("page_transition: Page '%s' not found", new_page_name);
		return -1;
	}
}
// 返回上次页面
int return_prev_page(void)
{
	// 检查栈是否有效
	if (stack == NULL)
	{
		ui_log("return_prev_page: Page stack is NULL");
		return -1;
	}

	char *prev_page_name = page_pop(stack);
	if (prev_page_name == NULL)
	{
		ui_log("NO PRE PAGE");
		return -1;
	}
	struct page *prev_page = page_search(prev_page_name);
	ui_log("%s", prev_page_name);
	if (prev_page != NULL)
	{
		// 检查当前页面是否有效
		if (cur_page == NULL)
		{
			ui_log("return_prev_page: Current page is NULL");
			cur_page = prev_page;
			if (prev_page->onInit != NULL)
			{
				prev_page->onInit(prev_page);
			}
			lv_scr_load_anim(prev_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
			return 0;
		}

		// 检查退出回调
		ui_log("return to page :%s", prev_page_name);
		if (cur_page->onExit != NULL)
		{
			cur_page->onExit(cur_page, prev_page);
		}
		else
		{
			ui_log("return_prev_page: Current page has NULL exit callback");
		}

		cur_page = prev_page;

		// 检查初始化回调
		if (prev_page->onInit != NULL)
		{
			prev_page->onInit(prev_page);
		}
		else
		{
			ui_log("return_prev_page: Previous page has NULL init callback");
		}

		lv_scr_load_anim(prev_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
		return 0;
	}
	else
	{
		ui_log("return_prev_page: Previous page '%s' not found", prev_page_name);
		return -1;
	}
}

// 只将页面压入到栈中,但是不显示
void page_push_no_display(const char *page_name)
{
	page_push(stack, (char *)page_name);
}

// 搜索特定弹窗
static struct pop *pop_search(const char *name)
{
	for (int i = 0; i < pop_num; i++)
	{
		if (strcmp(pop_admin[i].name, name) == 0)
		{
			return &pop_admin[i];
		}
	}
	return NULL;
}
// 开始弹窗
int pop_on(const char *pop_name)
{
	ui_log("%s", pop_name);
	if (strcmp(pop_name, "") == 0)
	{
		return -1;
	}

	struct pop *new_pop = pop_search(pop_name);
	if (new_pop != NULL)
	{
		if (cur_pop != NULL)
			cur_pop->onExit();
		cur_pop = new_pop;
		new_pop->onCreate(new_pop);
		new_pop->onStart();
		return 0;
	}
	else
	{
		return -1;
	}
}
// 退出弹窗
int pop_off(void)
{
	if (cur_pop != NULL)
	{
		ui_log("pop off");
		cur_pop->onExit();
		cur_pop = NULL;
		return 0;
	}
	return -1;
}

// 开始状态栏
int bar_on(const char *name)
{
	ui_log("start bar on");
	status_bar.onFresh(name);
	// status_bar.onCreate(&status_bar, name);
	// status_bar.onStart();
	ui_log("end bar on");
	return 0;
}
// 退出状态栏
int bar_off(void)
{
	// ui_log("start bar off");
	// status_bar.onExit();
	ui_log("end bar off");
	return 0;
}
// 修改状态栏名称
int bar_name(const char *name)
{
	status_bar.onRename(name);
	return 0;
}

int vitals_bar_on(void)
{
	sta_bar_vitals.onCreate(&sta_bar_vitals);
	sta_bar_vitals.onStart();
	return 0;
}

int vitals_bar_off(void)
{
	sta_bar_vitals.onExit();
	return 0;
}

int vitals_bar_update(uint8_t select)
{
	S_StatusBarVitalsInfo info;
	info.select = select;
	info_modify_StatusBarVitals(info);
	return 0;
}
