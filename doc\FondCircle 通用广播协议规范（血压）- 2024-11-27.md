## FondCircle 通用广播协议规范（血压）- 2024-11-27

**文档新建: 2024-11-27**

### 蓝牙名称要求

- 接入设备的蓝牙名称需唯一。
- **不同设备类型、型号、协议需使用不同的蓝牙名称**。

---

### 通用广播协议格式

> 适用于所有通过广播获取数据的设备（如体温计、血压计、血氧仪等）。

#### 定义通用场景

- 设备类型：血压仪扩展
- 新接入设备 **必须遵循通用广播协议**。

---

#### 一、广播内容

广播有效数据部分至少包含以下 **3 个 AD Structure**：

| **说明**       | **Length** | **AD Type** | **AD Data**                          | **占用字节** |
| -------------- | ---------- | ----------- | ------------------------------------ | ------------ |
| Flag 标识      | `0x02`     | `0x01`      | `0xXX`（示例：`0x06`）               | 3 字节       |
| 蓝牙名称       | `0xXX`     | `0x09`      | 蓝牙名称（尽量控制在 10 个字符以内） | N 字节       |
| 厂商自定义数据 | `0xXX`     | `0xFF`      | 规则见下文                           | N 字节       |

---

#### 二、厂商自定义数据规范

##### 1. 数据结构

- **Byte 0-5**：MAC 地址（大端）

  > 冗余字段，满足 iOS 需求（示例：`0xFF 0xFF 0x11 0x54 0xC2 0x84`）。

- **Byte 6-N**：自定义数据（根据业务需求定义）。

---

#### 三、通用血压厂商自定义数据格式

##### 1. 数据字段定义

| **字段**                | **字节位置**                             | **说明**                                                     |
| ----------------------- | ---------------------------------------- | ------------------------------------------------------------ |
| MAC 地址（大端）        | Byte 0-5                                 | 示例：`0xFF 0xFF 0x11 0x54 0xC2 0x84`                        |
| 错误码/状态码           | Byte 6                                   | `0x00`(无数据)、`0x01`(测量中)、`0x02`(测量结束)、`0x03`(错误) |
| 实时压力                | Byte 7                                   | 仅限测量中状态。                                             |
| 心率                    | Byte 8（测量中）<br>Byte 8（测量结束）   | 测量中与测量结束时复用。                                     |
| 舒张压 (8bit)           | Byte 7（测量结束）                       | 测量结束后发送。                                             |
| 收缩压 (8bit)           | Byte 8（测量结束）                       | 测量结束后发送。                                             |
| 测量序列号（0-255循环） | Byte 9（测量中）<br>Byte 9（测量结束）   | 每次测量递增。                                               |
| 异或校验位              | Byte 10（测量中）<br>Byte 10（测量结束） | `校验位 = 0x00 ^ 舒张压 ^ 收缩压 ^ 心率 ^ 序列号 ...` <br>（具体公式见下表） |

##### 2. 数据格式示例

| **状态码** | **数据说明**               | **校验公式**                                       |
| ---------- | -------------------------- | -------------------------------------------------- |
| `0x00`     | 无数据                     | 无校验                                             |
| `0x01`     | 测量中                     | `Byte(10) = Byte(6) ^ Byte(7) ^ Byte(8)`           |
| `0x02`     | 测量结束（收缩压和舒张压） | `Byte(11) = Byte(6) ^ Byte(7) ^ Byte(8) ^ Byte(9)` |
| `0x03`     | 错误                       | `Byte(10) = Byte(6) ^ 错误码` (具体字段需业务定义) |

---

### 格式转换说明

1. 统一保留原始文档的关键字段划分。
2. 使用表格清晰表达协议结构和字节定义。
3. 补充了注意事项（如广播时间窗口、校验规则）。
4. 高亮核心约束条件（如蓝牙名称长度限制）。

如果需要进一步调整格式或补充细节，请提供更多需求！