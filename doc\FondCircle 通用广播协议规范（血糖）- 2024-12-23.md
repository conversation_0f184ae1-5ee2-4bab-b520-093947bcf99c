

## FondCircle 通用广播协议规范（血糖）- 2024-12-23

**文档新建: 2024-12-23**

### 蓝牙名称要求
- 接入设备的蓝牙名称需唯一。
- **不同设备类型、型号、协议需使用不同的蓝牙名称**。

---

### 通用广播协议格式

> 适用于所有通过广播获取数据的设备（如体温计、血压计、血氧仪等）。

#### 定义通用场景
- 设备类型：血糖仪扩展
- 新接入设备 **必须遵循通用广播协议**。

---

#### 一、广播内容
广播有效数据部分至少包含以下 **3 个 AD Structure**：

| **说明**       | **Length** | **AD Type** | **AD Data**                          | **占用字节** |
| -------------- | ---------- | ----------- | ------------------------------------ | ------------ |
| Flag 标识      | `0x02`     | `0x01`      | `0xXX`（示例：`0x06`）               | 3 字节       |
| 蓝牙名称       | `0xXX`     | `0x09`      | 蓝牙名称（尽量控制在 10 个字符以内） | N 字节       |
| 厂商自定义数据 | `0xXX`     | `0xFF`      | 规则见下文                           | N 字节       |

---

#### 二、厂商自定义数据规范

##### 1. 数据结构
- **Byte 0-5**：MAC 地址（大端）
  > 冗余字段，满足 iOS 需求（示例：`0xFF 0xFF 0x11 0x54 0xC2 0x84`）。
- **Byte 6-N**：自定义数据（根据业务需求定义）。

---

#### 三、通用血糖厂商自定义数据格式

##### 1. 标准数据定义
| **字段**                | **字节位置** | **说明**                                                     |
| ----------------------- | ------------ | ------------------------------------------------------------ |
| MAC 地址（大端）        | Byte 0-5     | 示例：`0xFF 0xFF 0x11 0x54 0xC2 0x84`                        |
| 错误码/状态码           | Byte 6       | `0x00`(无数据)、`0x01`(测量中)、`0x02`(测量结束)、`0x03`(错误) |
| 血糖结果值 (mg/dL)      | Byte 7       | 示例：`0x01C3` → 451 mg/dL                                   |
| 测量序列号（0-255循环） | Byte 8       | 每次测量递增。                                               |
| 异或校验位              | Byte 9       | `Byte(9) = Byte(6) ^ Byte(7) ^ Byte(8)`                      |

##### 2. 说明
- **广播状态规则**：
  - `0x00`(无数据)：未测量状态。
  - `0x01`(测量中)： Normal 过程中，仅发送状态和 MAC 地址。
  - `0x02`(测量结束)：包含血糖结果、序列号和校验。
  - `0x03`(错误)：仅发送错误码和校验。
- **广播时间窗口**：
  - 测量结束或错误状态下，**2s 内以 20ms 频率发送**数据，之后回到无数据状态。

