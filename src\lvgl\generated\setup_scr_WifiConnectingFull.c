/*
 * Copyright 2024 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"

static S_WifiConnctingInfo info = {
    .state_info = NULL};

void setup_scr_WifiConnectingFull(struct page *page)
{
    // Write codes WifiConnectingFull
    WifiConnectingFull = page->body_obj;

    // Write codes WifiConnectingFull_Page
    WifiConnectingFull_Page = lv_obj_create(WifiConnectingFull);
    lv_obj_set_pos(WifiConnectingFull_Page, 0, 30);
    lv_obj_set_size(WifiConnectingFull_Page, 320, 210);
    lv_obj_set_scrollbar_mode(WifiConnectingFull_Page, LV_SCROLLBAR_MODE_OFF);

    // Write style for WifiConnect_Page, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(WifiConnectingFull_Page, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(WifiConnectingFull_Page, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(WifiConnectingFull_Page, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Write codes WifiConnectingFull_Gif
    WifiConnectingFull_Gif = lv_animimg_create(WifiConnectingFull_Page);
    lv_animimg_set_src(WifiConnectingFull_Gif, (const void **)WifiConnecting_ContGif_imgs, 30);
    lv_animimg_set_duration(WifiConnectingFull_Gif, 30 * 30);
    lv_animimg_set_repeat_count(WifiConnectingFull_Gif, LV_ANIM_REPEAT_INFINITE);
    lv_animimg_start(WifiConnectingFull_Gif);
    lv_obj_set_align(WifiConnectingFull_Gif, LV_ALIGN_CENTER);
    lv_obj_align(WifiConnectingFull_Gif, LV_ALIGN_TOP_MID, 0, 50);
    lv_obj_set_size(WifiConnectingFull_Gif, 80, 80);

    // Write codes WifiConnectingFull_DetailText
    WifiConnectingFull_DetailText = lv_label_create(WifiConnectingFull_Page);

    lv_label_set_long_mode(WifiConnectingFull_DetailText, LV_LABEL_LONG_WRAP);
    lv_obj_set_align(WifiConnectingFull_DetailText, LV_ALIGN_CENTER);
    lv_obj_align(WifiConnectingFull_DetailText, LV_ALIGN_TOP_MID, 0, 135);
    lv_obj_set_size(WifiConnectingFull_DetailText, 200, 20);
    if (info.state_info == NULL)
    {
        lv_label_set_text(WifiConnectingFull_DetailText, "Connecting to Wi-Fi...");
    }
    else
    {
        lv_label_set_text_fmt(WifiConnectingFull_DetailText, "%s", info.state_info);
    }

    // Write style for WifiOperation_ContDetailText, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(WifiConnectingFull_DetailText, lv_color_hex(0x666666), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(WifiConnectingFull_DetailText, &lv_font_sf_compact_m_14, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(WifiConnectingFull_DetailText, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(WifiConnectingFull_DetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    // Write codes WifiConnectingFull_ContImg
    WifiConnectingFull_ContImg = lv_label_create(WifiConnectingFull_Page);
    lv_label_set_text(WifiConnectingFull_ContImg, WIFI_CONNECTING_ICON);
    lv_label_set_long_mode(WifiConnectingFull_ContImg, LV_LABEL_LONG_WRAP);
    lv_obj_align(WifiConnectingFull_ContImg, LV_ALIGN_TOP_MID, 0, 20);
    lv_obj_set_pos(WifiConnectingFull_ContImg, 0, 20);

    // Write style for WifiConnectingFull_ContImg, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(WifiConnectingFull_ContImg, lv_color_hex(EMPHASIS_COLOR), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(WifiConnectingFull_ContImg, &lv_font_iconfont_40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(WifiConnectingFull_ContImg, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(WifiConnectingFull_ContImg, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(WifiConnectingFull_ContImg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    bar_on("Fond_Circle");

    // lv_scr_load_anim(page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
}

void exit_scr_WifiConnectingFull(struct page *old_page, struct page *new_page)
{
    ui_log("start exit_scr_WifiConnectingFull");
    bar_off();
    ui_log("old_page->name:%s", old_page->name);
    lv_obj_clean(old_page->body_obj);
    ui_log("22");
    // lv_scr_load_anim(new_page->body_obj, LV_SCR_LOAD_ANIM_FADE_ON, 500, 0, false);
    ui_log("end exit_scr_WifiConnectingFull");
}

void info_modify_WifiConnectingFull(S_WifiConnctingInfo info_data)
{
    if (info_data.state_info != NULL)
    {
        if (info.state_info != NULL)
            free(info.state_info);
        info.state_info = (char *)ps_malloc(strlen(info_data.state_info) + 1);
        strcpy(info.state_info, info_data.state_info);
    }

    // 更新当前页面的控件
    if (get_cur_page()->body_obj == WifiConnectingFull)
    {
        lv_label_set_text(WifiConnectingFull_DetailText, info.state_info);
    }
}

void info_modify_WifiConnectingFull_info_reset(void)
{
    if (info.state_info != NULL)
    {
        free(info.state_info);
        info.state_info = NULL;
    }
}
