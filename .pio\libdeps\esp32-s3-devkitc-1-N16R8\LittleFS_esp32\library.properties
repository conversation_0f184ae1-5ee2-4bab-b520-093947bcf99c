name=LittleFS_esp32
version=1.0.6
author=lorol
maintainer=lorol
sentence=LittleFS for esp32 based on esp_littlefs IDF component. Use esp32 core-provided LITTLEFS library instead of this one when available in future core releases.
paragraph= For esp32 core 1.0.4 release, use #define CONFIG_LITTLEFS_FOR_IDF_3_2 and for more SPIFFS compatibility, set #define CONFIG_LITTLEFS_SPIFFS_COMPAT 1
category=Data Storage
url=https://github.com/lorol/LITTLEFS
architectures=esp32