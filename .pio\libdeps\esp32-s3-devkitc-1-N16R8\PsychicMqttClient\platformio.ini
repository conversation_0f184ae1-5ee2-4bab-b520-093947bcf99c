[env]
framework = arduino
build_flags = 
  -Wall -Wextra
  -D CONFIG_ARDUHAL_LOG_COLORS
  -D CORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_NONE
lib_deps = 
upload_protocol = esptool
monitor_speed = 115200
monitor_filters = esp32_exception_decoder, log2file

extra_scripts = pre:scripts/generate_cert_bundle.py
; Source for SSL Cert Store can bei either downloaded from Mozilla with 'mozilla' ('https://curl.se/ca/cacert.pem')
; or from a curated Adafruit repository with 'adafruit' (https://raw.githubusercontent.com/adafruit/certificates/main/data/roots.pem)
; or complied from a 'folder' full of *.pem / *.dem files stored in the ./ssl_certs folder
board_ssl_cert_source = adafruit
board_build.embed_files = src/certs/x509_crt_bundle.bin

[platformio]
lib_dir = .
; src_dir = examples/FullyFeatured
src_dir = examples/Simple
; src_dir = examples/Simple_WS
; src_dir = examples/SSL_CA_Bundle
; src_dir = examples/SSL_CA_Bundle_WiFiClientSecure
; src_dir = examples/SSL_CA_Cert
; src_dir = examples/Wildcards

[env:arduino-2]
platform = espressif32@6.8.1
board = esp32dev

[env:arduino-3]
platform = espressif32
platform_packages=
  platformio/framework-arduinoespressif32 @ https://github.com/espressif/arduino-esp32.git#3.0.4
  platformio/framework-arduinoespressif32-libs @ https://github.com/espressif/arduino-esp32/releases/download/3.0.4/esp32-arduino-libs-3.0.4.zip
board = esp32dev

[env:pioarduino-3]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/53.03.10/platform-espressif32.zip
board = esp32dev
