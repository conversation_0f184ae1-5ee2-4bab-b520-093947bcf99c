#!/usr/bin/env python3
"""
语音文件嵌入脚本
将语音文件压缩并嵌入到Flash ROM分区中
"""

import os
import sys
import struct
import hashlib
import zlib
import time
import argparse
from pathlib import Path

# 常量定义
VOICE_ROM_MAGIC = 0x564F4943  # "VOIC"
VOICE_ROM_VERSION_MAJOR = 1
VOICE_ROM_VERSION_MINOR = 0
MAX_FILENAME_LEN = 32
VOICE_COMPRESSION_NONE = 0
VOICE_COMPRESSION_ADPCM = 1
VOICE_COMPRESSION_ULAW = 2

class VoiceFileEntry:
    """语音文件条目"""
    def __init__(self, filename, data, compression_type=VOICE_COMPRESSION_NONE):
        self.filename = filename[:MAX_FILENAME_LEN-1]  # 确保不超过最大长度
        self.original_data = data
        self.original_size = len(data)
        self.compression_type = compression_type
        self.compressed_data = self.compress_data()
        self.compressed_size = len(self.compressed_data)
        self.checksum = self.calculate_checksum()
        self.version = 1
        self.offset = 0  # 将在打包时设置

    def compress_data(self):
        """压缩数据"""
        if self.compression_type == VOICE_COMPRESSION_NONE:
            return self.original_data
        elif self.compression_type == VOICE_COMPRESSION_ADPCM:
            # 简化：这里应该实现ADPCM压缩，暂时返回原数据
            print(f"Warning: ADPCM compression not implemented for {self.filename}")
            return self.original_data
        elif self.compression_type == VOICE_COMPRESSION_ULAW:
            # 简化：这里应该实现μ-law压缩，暂时返回原数据
            print(f"Warning: μ-law compression not implemented for {self.filename}")
            return self.original_data
        else:
            return self.original_data

    def calculate_checksum(self):
        """计算校验和"""
        return zlib.crc32(self.compressed_data) & 0xffffffff

    def pack_entry(self):
        """打包文件条目"""
        filename_bytes = self.filename.encode('utf-8')
        filename_padded = filename_bytes + b'\x00' * (MAX_FILENAME_LEN - len(filename_bytes))
        
        return struct.pack('<32sIIIIHBB',
                          filename_padded,
                          self.offset,
                          self.compressed_size,
                          self.original_size,
                          self.checksum,
                          self.version,
                          self.compression_type,
                          0)  # reserved

class VoiceROMBuilder:
    """语音ROM构建器"""
    def __init__(self):
        self.entries = []
        self.build_number = int(time.time())

    def add_file(self, filepath, compression_type=VOICE_COMPRESSION_NONE):
        """添加语音文件"""
        if not os.path.exists(filepath):
            print(f"Error: File not found: {filepath}")
            return False

        filename = os.path.splitext(os.path.basename(filepath))[0]  # 去掉扩展名
        
        try:
            with open(filepath, 'rb') as f:
                data = f.read()
            
            entry = VoiceFileEntry(filename, data, compression_type)
            self.entries.append(entry)
            print(f"Added file: {filename} ({len(data)} bytes)")
            return True
        except Exception as e:
            print(f"Error reading file {filepath}: {e}")
            return False

    def build_rom(self, output_path):
        """构建ROM文件"""
        if not self.entries:
            print("Error: No files to build")
            return False

        try:
            with open(output_path, 'wb') as f:
                # 计算偏移量
                header_size = 32  # sizeof(voice_rom_header_t)
                entries_size = len(self.entries) * 40  # sizeof(voice_file_entry_t)
                data_offset = header_size + entries_size

                # 设置每个文件的偏移量
                current_offset = data_offset
                for entry in self.entries:
                    entry.offset = current_offset
                    current_offset += entry.compressed_size

                total_size = current_offset

                # 写入头部
                header = self.pack_header(total_size)
                f.write(header)

                # 写入文件条目
                for entry in self.entries:
                    f.write(entry.pack_entry())

                # 写入文件数据
                for entry in self.entries:
                    f.write(entry.compressed_data)

                print(f"ROM built successfully: {output_path}")
                print(f"Total size: {total_size} bytes")
                print(f"Files: {len(self.entries)}")
                return True

        except Exception as e:
            print(f"Error building ROM: {e}")
            return False

    def pack_header(self, total_size):
        """打包ROM头部"""
        header_data = struct.pack('<IHHIII',
                                 VOICE_ROM_MAGIC,
                                 VOICE_ROM_VERSION_MAJOR,
                                 VOICE_ROM_VERSION_MINOR,
                                 self.build_number,
                                 int(time.time()),
                                 len(self.entries))
        
        header_data += struct.pack('<II', total_size, 0)  # total_size, checksum placeholder
        header_data += b'\x00' * 8  # reserved
        
        # 计算头部校验和
        checksum = zlib.crc32(header_data[:-12]) & 0xffffffff  # 排除checksum和reserved字段
        
        # 重新打包包含校验和的头部
        header_data = struct.pack('<IHHIIIIII',
                                 VOICE_ROM_MAGIC,
                                 VOICE_ROM_VERSION_MAJOR,
                                 VOICE_ROM_VERSION_MINOR,
                                 self.build_number,
                                 int(time.time()),
                                 len(self.entries),
                                 total_size,
                                 checksum,
                                 0)  # reserved
        
        return header_data

def main():
    parser = argparse.ArgumentParser(description='Embed voice files into ROM')
    parser.add_argument('--input-dir', '-i', required=True, help='Input directory containing voice files')
    parser.add_argument('--output', '-o', required=True, help='Output ROM file')
    parser.add_argument('--compression', '-c', choices=['none', 'adpcm', 'ulaw'], 
                       default='none', help='Compression type')
    parser.add_argument('--file-list', '-f', help='Text file containing list of files to include')
    
    args = parser.parse_args()

    # 映射压缩类型
    compression_map = {
        'none': VOICE_COMPRESSION_NONE,
        'adpcm': VOICE_COMPRESSION_ADPCM,
        'ulaw': VOICE_COMPRESSION_ULAW
    }
    compression_type = compression_map[args.compression]

    builder = VoiceROMBuilder()

    # 确定要处理的文件列表
    files_to_process = []
    
    if args.file_list:
        # 从文件列表读取
        try:
            with open(args.file_list, 'r') as f:
                for line in f:
                    filename = line.strip()
                    if filename and not filename.startswith('#'):
                        files_to_process.append(filename)
        except Exception as e:
            print(f"Error reading file list: {e}")
            return 1
    else:
        # 扫描输入目录
        input_path = Path(args.input_dir)
        if not input_path.exists():
            print(f"Error: Input directory not found: {args.input_dir}")
            return 1
        
        for file_path in input_path.glob('*.wav'):
            files_to_process.append(file_path.name)

    # 处理文件
    success_count = 0
    for filename in files_to_process:
        filepath = os.path.join(args.input_dir, filename)
        if builder.add_file(filepath, compression_type):
            success_count += 1

    if success_count == 0:
        print("Error: No files were successfully added")
        return 1

    # 构建ROM
    if builder.build_rom(args.output):
        print(f"Successfully embedded {success_count} voice files")
        return 0
    else:
        return 1

if __name__ == '__main__':
    sys.exit(main())
