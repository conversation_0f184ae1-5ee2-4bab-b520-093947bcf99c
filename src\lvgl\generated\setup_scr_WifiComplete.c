/*
 * Copyright 2024 NXP
 * NXP Confidential and Proprietary. This software is owned or controlled by NXP and may only be used strictly in
 * accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
 * activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
 * terms, then you may not retain, install, activate or otherwise use the software.
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"

static lv_anim_timeline_t *anim_timeline = NULL;
void setup_scr_WifiComplete(struct pop *pop)
{
	// Write codes WifiComplete_PageCont
	WifiComplete_PageCont = pop->body_obj;
	lv_obj_set_pos(WifiComplete_PageCont, 50, -220);
	lv_obj_set_size(WifiComplete_PageCont, 220, 200);
	lv_obj_set_scrollbar_mode(WifiComplete_PageCont, LV_SCROLLBAR_MODE_OFF);
	// Write style for WifiComplete_PageCont, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiComplete_PageCont, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiComplete_PageCont, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(WifiComplete_PageCont, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_color(WifiComplete_PageCont, lv_color_black(), 0);
	lv_obj_set_style_shadow_width(WifiComplete_PageCont, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiComplete_ContDetailText
	WifiComplete_ContDetailText = lv_label_create(WifiComplete_PageCont);
	lv_label_set_text(WifiComplete_ContDetailText, "Network configuration successful");
	lv_label_set_long_mode(WifiComplete_ContDetailText, LV_LABEL_LONG_WRAP);
	lv_obj_set_pos(WifiComplete_ContDetailText, 10, 126);
	lv_obj_set_size(WifiComplete_ContDetailText, 200, 30);

	// Write style for WifiComplete_ContDetailText, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiComplete_ContDetailText, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiComplete_ContDetailText, &lv_font_sf_compact_m_14, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_letter_space(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_line_space(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiComplete_ContDetailText, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiComplete_ContDetailText, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	// Write codes WifiComplete_ContIcon
	WifiComplete_ContIcon = lv_label_create(WifiComplete_PageCont);
	lv_label_set_text(WifiComplete_ContIcon, SUCCESS_ICON);
	lv_label_set_long_mode(WifiComplete_ContIcon, LV_LABEL_LONG_WRAP);
	lv_obj_set_pos(WifiComplete_ContIcon, 82, 56);
	lv_obj_set_size(WifiComplete_ContIcon, 60, 60);

	// Write style for WifiComplete_ContIcon, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
	lv_obj_set_style_border_width(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_radius(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_color(WifiComplete_ContIcon, lv_color_hex(EMPHASIS_COLOR), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(WifiComplete_ContIcon, &lv_font_iconfont_60, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_letter_space(WifiComplete_ContIcon, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_line_space(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_align(WifiComplete_ContIcon, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_shadow_width(WifiComplete_ContIcon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	if (anim_timeline == NULL)
	{
		anim_timeline = lv_anim_timeline_create();
#define ANIM_DEF(start_time, obj, attr, start, end) \
	{start_time, obj, LV_ANIM_EXEC(attr), start, end, 500, lv_anim_path_bounce, true}

		lv_anim_timeline_wrapper_t wrapper[] =
			{
				ANIM_DEF(0, WifiComplete_PageCont, y, -220, 20),
				LV_ANIM_TIMELINE_WRAPPER_END};
#undef ANIM_DEF
		lv_anim_timeline_add_wrapper(anim_timeline, wrapper);
	}
}

void start_scr_WifiComplete(void)
{
	lv_coord_t y = lv_obj_get_y(WifiComplete_PageCont);

	lv_anim_timeline_set_reverse(anim_timeline, 0);
	lv_anim_timeline_start(anim_timeline);

	lv_obj_set_style_bg_opa(lv_layer_sys(), LV_OPA_50, 0); // 设置对象透明度
	lv_obj_set_style_bg_color(lv_layer_sys(), lv_palette_main(LV_PALETTE_GREY), 0);
}

void exit_scr_WifiComplete(void)
{
	lv_coord_t y = lv_obj_get_y(WifiComplete_PageCont);

	lv_anim_timeline_set_reverse(anim_timeline, 1);
	lv_anim_timeline_start(anim_timeline);
	lv_obj_clean(WifiComplete_PageCont);

	lv_obj_set_style_bg_opa(lv_layer_sys(), LV_OPA_0, 0); // 设置对象透明度
	lv_obj_set_style_bg_color(lv_layer_sys(), lv_palette_main(LV_PALETTE_GREY), 0);
}
