#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <string.h>

static const char *TAG = "VoiceCompression";

// ADPCM相关常量和表格
static const int16_t adpcm_step_table[89] = {
    7, 8, 9, 10, 11, 12, 13, 14, 16, 17,
    19, 21, 23, 25, 28, 31, 34, 37, 41, 45,
    50, 55, 60, 66, 73, 80, 88, 97, 107, 118,
    130, 143, 157, 173, 190, 209, 230, 253, 279, 307,
    337, 371, 408, 449, 494, 544, 598, 658, 724, 796,
    876, 963, 1060, 1166, 1282, 1411, 1552, 1707, 1878, 2066,
    2272, 2499, 2749, 3024, 3327, 3660, 4026, 4428, 4871, 5358,
    5894, 6484, 7132, 7845, 8630, 9493, 10442, 11487, 12635, 13899,
    15289, 16818, 18500, 20350, 22385, 24623, 27086, 29794, 32767};

static const int8_t adpcm_index_table[16] = {
    -1, -1, -1, -1, 2, 4, 6, 8,
    -1, -1, -1, -1, 2, 4, 6, 8};

// ADPCM状态结构
typedef struct
{
    int16_t predicted_value;
    int8_t step_index;
} adpcm_state_t;

// μ-law编码表
static const uint16_t mulaw_bias = 0x84;
static const uint16_t mulaw_clip = 32635;

// 内部函数声明
static voice_error_t adpcm_decode(const uint8_t *input, uint32_t input_size,
                                  int16_t *output, uint32_t output_samples);
static voice_error_t adpcm_encode(const int16_t *input, uint32_t input_samples,
                                  uint8_t *output, uint32_t *output_size);
static voice_error_t mulaw_decode(const uint8_t *input, uint32_t input_size,
                                  int16_t *output, uint32_t output_samples);
static voice_error_t mulaw_encode(const int16_t *input, uint32_t input_samples,
                                  uint8_t *output, uint32_t *output_size);
static int16_t adpcm_decode_sample(uint8_t code, adpcm_state_t *state);
static uint8_t adpcm_encode_sample(int16_t sample, adpcm_state_t *state);
static uint8_t linear_to_mulaw(int16_t sample);
static int16_t mulaw_to_linear(uint8_t mulaw);

/**
 * @brief 解压缩数据
 */
voice_error_t voice_decompress(const uint8_t *compressed_data, uint32_t compressed_size,
                               uint8_t *output_buffer, uint32_t output_size,
                               uint8_t compression_type)
{
    if (!compressed_data || !output_buffer || compressed_size == 0 || output_size == 0)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGD(TAG, "Decompressing %d bytes with type %d", compressed_size, compression_type);

    switch (compression_type)
    {
    case VOICE_COMPRESSION_NONE:
        // 无压缩，直接复制
        if (output_size < compressed_size)
        {
            ESP_LOGE(TAG, "Output buffer too small for uncompressed data");
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }
        memcpy(output_buffer, compressed_data, compressed_size);
        break;

    case VOICE_COMPRESSION_ADPCM:
        // ADPCM解压缩
        return adpcm_decode(compressed_data, compressed_size,
                            (int16_t *)output_buffer, output_size / 2);

    case VOICE_COMPRESSION_ULAW:
        // μ-law解压缩
        return mulaw_decode(compressed_data, compressed_size,
                            (int16_t *)output_buffer, output_size / 2);

    case VOICE_COMPRESSION_OPUS:
        ESP_LOGE(TAG, "Opus compression not implemented yet");
        return VOICE_ERR_DECOMPRESSION_FAILED;

    default:
        ESP_LOGE(TAG, "Unknown compression type: %d", compression_type);
        return VOICE_ERR_INVALID_PARAM;
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 压缩数据
 */
voice_error_t voice_compress(const uint8_t *input_data, uint32_t input_size,
                             uint8_t *output_buffer, uint32_t *output_size,
                             uint8_t compression_type)
{
    if (!input_data || !output_buffer || !output_size || input_size == 0)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGD(TAG, "Compressing %d bytes with type %d", input_size, compression_type);

    switch (compression_type)
    {
    case VOICE_COMPRESSION_NONE:
        // 无压缩，直接复制
        if (*output_size < input_size)
        {
            ESP_LOGE(TAG, "Output buffer too small for uncompressed data");
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }
        memcpy(output_buffer, input_data, input_size);
        *output_size = input_size;
        break;

    case VOICE_COMPRESSION_ADPCM:
        // ADPCM压缩
        return adpcm_encode((const int16_t *)input_data, input_size / 2,
                            output_buffer, output_size);

    case VOICE_COMPRESSION_ULAW:
        // μ-law压缩
        return mulaw_encode((const int16_t *)input_data, input_size / 2,
                            output_buffer, output_size);

    case VOICE_COMPRESSION_OPUS:
        ESP_LOGE(TAG, "Opus compression not implemented yet");
        return VOICE_ERR_DECOMPRESSION_FAILED;

    default:
        ESP_LOGE(TAG, "Unknown compression type: %d", compression_type);
        return VOICE_ERR_INVALID_PARAM;
    }

    return VOICE_ERR_OK;
}

/**
 * @brief ADPCM解码
 */
static voice_error_t adpcm_decode(const uint8_t *input, uint32_t input_size,
                                  int16_t *output, uint32_t output_samples)
{
    if (input_size < 4)
    { // 至少需要4字节的头部
        return VOICE_ERR_INVALID_PARAM;
    }

    // 读取初始状态
    adpcm_state_t state;
    state.predicted_value = (int16_t)((input[1] << 8) | input[0]);
    state.step_index = input[2];

    if (state.step_index >= 89)
    {
        state.step_index = 88;
    }

    uint32_t samples_decoded = 0;
    uint32_t byte_index = 4; // 跳过4字节头部

    // 解码数据
    while (byte_index < input_size && samples_decoded < output_samples)
    {
        uint8_t byte_data = input[byte_index++];

        // 每个字节包含2个4位样本
        if (samples_decoded < output_samples)
        {
            output[samples_decoded++] = adpcm_decode_sample(byte_data & 0x0F, &state);
        }
        if (samples_decoded < output_samples)
        {
            output[samples_decoded++] = adpcm_decode_sample((byte_data >> 4) & 0x0F, &state);
        }
    }

    ESP_LOGD(TAG, "ADPCM decoded %d samples from %d bytes", samples_decoded, input_size);
    return VOICE_ERR_OK;
}

/**
 * @brief ADPCM编码
 */
static voice_error_t adpcm_encode(const int16_t *input, uint32_t input_samples,
                                  uint8_t *output, uint32_t *output_size)
{
    if (*output_size < (input_samples / 2 + 4))
    {
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    adpcm_state_t state;
    state.predicted_value = input[0];
    state.step_index = 0;

    // 写入头部
    output[0] = state.predicted_value & 0xFF;
    output[1] = (state.predicted_value >> 8) & 0xFF;
    output[2] = state.step_index;
    output[3] = 0; // 保留字节

    uint32_t byte_index = 4;
    uint32_t sample_index = 1; // 跳过第一个样本（已作为初始值）

    // 编码数据
    while (sample_index < input_samples)
    {
        uint8_t code1 = 0, code2 = 0;

        if (sample_index < input_samples)
        {
            code1 = adpcm_encode_sample(input[sample_index++], &state);
        }
        if (sample_index < input_samples)
        {
            code2 = adpcm_encode_sample(input[sample_index++], &state);
        }

        output[byte_index++] = (code2 << 4) | code1;
    }

    *output_size = byte_index;
    ESP_LOGD(TAG, "ADPCM encoded %d samples to %d bytes", input_samples, byte_index);
    return VOICE_ERR_OK;
}

/**
 * @brief ADPCM解码单个样本
 */
static int16_t adpcm_decode_sample(uint8_t code, adpcm_state_t *state)
{
    int32_t step = adpcm_step_table[state->step_index];
    int32_t diff = step >> 3;

    if (code & 4)
        diff += step;
    if (code & 2)
        diff += step >> 1;
    if (code & 1)
        diff += step >> 2;
    if (code & 8)
        diff = -diff;

    state->predicted_value += diff;

    // 限制范围
    if (state->predicted_value > 32767)
    {
        state->predicted_value = 32767;
    }
    else if (state->predicted_value < -32768)
    {
        state->predicted_value = -32768;
    }

    // 更新步长索引
    state->step_index += adpcm_index_table[code];
    if (state->step_index < 0)
    {
        state->step_index = 0;
    }
    else if (state->step_index >= 89)
    {
        state->step_index = 88;
    }

    return state->predicted_value;
}

/**
 * @brief ADPCM编码单个样本
 */
static uint8_t adpcm_encode_sample(int16_t sample, adpcm_state_t *state)
{
    int32_t diff = sample - state->predicted_value;
    uint8_t code = 0;

    if (diff < 0)
    {
        code = 8;
        diff = -diff;
    }

    int32_t step = adpcm_step_table[state->step_index];
    int32_t delta = step >> 3;

    if (diff >= step)
    {
        code |= 4;
        diff -= step;
        delta += step;
    }

    step >>= 1;
    if (diff >= step)
    {
        code |= 2;
        diff -= step;
        delta += step;
    }

    step >>= 1;
    if (diff >= step)
    {
        code |= 1;
        delta += step;
    }

    if (code & 8)
    {
        state->predicted_value -= delta;
    }
    else
    {
        state->predicted_value += delta;
    }

    // 限制范围
    if (state->predicted_value > 32767)
    {
        state->predicted_value = 32767;
    }
    else if (state->predicted_value < -32768)
    {
        state->predicted_value = -32768;
    }

    // 更新步长索引
    state->step_index += adpcm_index_table[code];
    if (state->step_index < 0)
    {
        state->step_index = 0;
    }
    else if (state->step_index >= 89)
    {
        state->step_index = 88;
    }

    return code;
}

/**
 * @brief μ-law解码
 */
static voice_error_t mulaw_decode(const uint8_t *input, uint32_t input_size,
                                  int16_t *output, uint32_t output_samples)
{
    if (input_size > output_samples)
    {
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    for (uint32_t i = 0; i < input_size && i < output_samples; i++)
    {
        output[i] = mulaw_to_linear(input[i]);
    }

    ESP_LOGD(TAG, "μ-law decoded %d samples", input_size);
    return VOICE_ERR_OK;
}

/**
 * @brief μ-law编码
 */
static voice_error_t mulaw_encode(const int16_t *input, uint32_t input_samples,
                                  uint8_t *output, uint32_t *output_size)
{
    if (*output_size < input_samples)
    {
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    for (uint32_t i = 0; i < input_samples; i++)
    {
        output[i] = linear_to_mulaw(input[i]);
    }

    *output_size = input_samples;
    ESP_LOGD(TAG, "μ-law encoded %d samples", input_samples);
    return VOICE_ERR_OK;
}

/**
 * @brief 线性PCM转μ-law
 */
static uint8_t linear_to_mulaw(int16_t sample)
{
    int16_t sign = (sample >> 8) & 0x80;
    if (sign != 0)
    {
        sample = -sample;
    }

    if (sample > mulaw_clip)
    {
        sample = mulaw_clip;
    }

    sample += mulaw_bias;

    int16_t exponent = 7;
    int16_t mantissa;

    for (int16_t exp_mask = 0x4000; (sample & exp_mask) == 0 && exponent > 0; exp_mask >>= 1, exponent--)
        ;

    mantissa = (sample >> (exponent + 3)) & 0x0F;

    return ~(sign | (exponent << 4) | mantissa);
}

/**
 * @brief μ-law转线性PCM
 */
static int16_t mulaw_to_linear(uint8_t mulaw)
{
    mulaw = ~mulaw;

    int16_t sign = mulaw & 0x80;
    int16_t exponent = (mulaw >> 4) & 0x07;
    int16_t mantissa = mulaw & 0x0F;

    int16_t sample = ((mantissa << 3) + mulaw_bias) << exponent;
    sample -= mulaw_bias;

    return (sign != 0) ? -sample : sample;
}
