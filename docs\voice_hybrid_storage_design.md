# BLE网关语音播报混合存储架构设计

## 1. 架构概述

本设计实现了Flash预置语音文件与动态下载语音文件的混合存储方案，提供快速响应、智能更新和可靠回退的语音播报功能。

## 2. 存储分区规划

### 2.1 Flash分区调整
```csv
# 新的分区表 (ble_gateway_partition_v2.csv)
# Name,     Type, SubType,  Offset,   Size,     Flags
nvs,        data, nvs,      0x9000,   0x5000,
otadata,    data, ota,      0xe000,   0x2000,
app0,       app,  ota_0,    0x10000,  0x2E0000,  # 减少40KB
app1,       app,  ota_1,    0x2F0000, 0x2E0000,  # 减少40KB
voice_rom,  data, spiffs,   0x5D0000, 0x50000,   # 新增320KB预置语音分区
spiffs,     data, spiffs,   0x620000, 0x9D0000,  # 调整LittleFS起始位置
coredump,   data, coredump, 0xFF0000, 0x10000,
```

### 2.2 存储层次结构
```
语音文件存储层次：
├── Flash ROM (voice_rom) - 320KB
│   ├── 基础语音文件 (压缩存储)
│   ├── 版本信息
│   └── 文件索引表
├── LittleFS (/audio) - 动态存储
│   ├── 云端下载的语音文件
│   ├── 用户自定义语音
│   └── 临时缓存文件
└── RAM缓存
    ├── 当前播放文件
    └── 预加载文件
```

## 3. 文件版本管理

### 3.1 版本标识系统
```cpp
typedef struct {
    uint32_t magic;           // 魔数标识: 0x564F4943 ("VOIC")
    uint16_t major_version;   // 主版本号
    uint16_t minor_version;   // 次版本号
    uint32_t build_number;    // 构建号
    uint32_t timestamp;       // 构建时间戳
    uint32_t file_count;      // 文件数量
    uint32_t total_size;      // 总大小
    uint32_t checksum;        // 校验和
} voice_rom_header_t;

typedef struct {
    char filename[32];        // 文件名
    uint32_t offset;          // 在ROM中的偏移
    uint32_t compressed_size; // 压缩后大小
    uint32_t original_size;   // 原始大小
    uint32_t checksum;        // 文件校验和
    uint16_t version;         // 文件版本
    uint8_t compression_type; // 压缩类型
    uint8_t reserved;         // 保留字段
} voice_file_entry_t;
```

### 3.2 版本比较策略
```cpp
enum voice_version_compare_result {
    VERSION_SAME = 0,
    VERSION_ROM_NEWER = 1,
    VERSION_CLOUD_NEWER = 2,
    VERSION_INVALID = -1
};
```

## 4. 优先级策略

### 4.1 文件选择优先级
1. **RAM缓存** - 最高优先级，即时播放
2. **LittleFS动态文件** - 云端下载的最新版本
3. **Flash ROM预置文件** - 基础版本，保证可用性
4. **默认提示音** - 最后的回退选项

### 4.2 智能选择算法
```cpp
typedef enum {
    VOICE_SOURCE_RAM_CACHE,
    VOICE_SOURCE_LITTLEFS,
    VOICE_SOURCE_FLASH_ROM,
    VOICE_SOURCE_DEFAULT_TONE,
    VOICE_SOURCE_NONE
} voice_source_type_t;

typedef struct {
    voice_source_type_t source;
    char file_path[64];
    uint32_t file_size;
    uint16_t version;
    bool is_compressed;
} voice_file_info_t;
```

## 5. 压缩算法选择

### 5.1 音频压缩方案
- **ADPCM (IMA/DVI)**: 4:1压缩比，低CPU占用
- **μ-law/A-law**: 2:1压缩比，极低CPU占用  
- **Opus**: 高压缩比，但CPU占用较高

### 5.2 推荐配置
```cpp
#define VOICE_COMPRESSION_ADPCM     1
#define VOICE_COMPRESSION_ULAW      2
#define VOICE_COMPRESSION_OPUS      3

// 默认使用ADPCM压缩
#define DEFAULT_VOICE_COMPRESSION   VOICE_COMPRESSION_ADPCM
```

## 6. API接口设计

### 6.1 核心接口
```cpp
// 初始化语音系统
esp_err_t voice_hybrid_init(void);

// 播放语音文件
esp_err_t voice_play(const char* voice_name);

// 检查文件版本
voice_version_compare_result voice_check_version(const char* voice_name);

// 更新语音文件
esp_err_t voice_update_from_cloud(const char* voice_name);

// 获取存储状态
esp_err_t voice_get_storage_info(voice_storage_info_t* info);
```

### 6.2 配置接口
```cpp
// 设置优先级策略
esp_err_t voice_set_priority_policy(voice_priority_policy_t policy);

// 设置缓存大小
esp_err_t voice_set_cache_size(uint32_t size_kb);

// 启用/禁用压缩
esp_err_t voice_set_compression_enabled(bool enabled);
```

## 7. 错误处理和回退机制

### 7.1 错误类型定义
```cpp
typedef enum {
    VOICE_ERR_OK = 0,
    VOICE_ERR_FILE_NOT_FOUND,
    VOICE_ERR_DECOMPRESSION_FAILED,
    VOICE_ERR_CHECKSUM_MISMATCH,
    VOICE_ERR_INSUFFICIENT_MEMORY,
    VOICE_ERR_NETWORK_TIMEOUT,
    VOICE_ERR_STORAGE_FULL
} voice_error_t;
```

### 7.2 回退策略
1. 文件损坏 → 尝试重新下载 → 使用ROM版本 → 使用默认提示音
2. 网络异常 → 使用本地缓存 → 使用ROM版本
3. 存储空间不足 → 清理旧文件 → 使用ROM版本

## 8. 性能优化

### 8.1 预加载策略
- 系统启动时预加载常用语音文件到RAM
- 根据使用频率动态调整预加载列表
- 支持后台异步预加载

### 8.2 缓存管理
- LRU (Least Recently Used) 缓存淘汰算法
- 智能预测和预加载
- 内存压力感知的缓存调整

## 9. 实现阶段规划

### 阶段1: 基础架构
- Flash分区调整
- 基础数据结构定义
- ROM文件系统实现

### 阶段2: 压缩和解压
- 集成ADPCM压缩算法
- 实现解压缩接口
- 性能测试和优化

### 阶段3: 智能选择
- 实现优先级算法
- 版本比较和更新机制
- 错误处理和回退

### 阶段4: 优化和测试
- 性能优化
- 全面测试
- 文档完善
