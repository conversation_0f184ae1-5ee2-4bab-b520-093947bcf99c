{"C_Cpp.errorSquiggles": "disabled", "cmake.configureOnOpen": false, "idf.portWin": "COM19", "idf.adapterTargetName": "esp32s3", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "C_Cpp.default.compilerPath": "C:/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3/bin/xtensa-esp32s3-elf-gcc.exe", "commentTranslate.source": "Kaiqun.tencent-cloud-translate-tencentCloud", "C_Cpp.intelliSenseEngine": "default", "idf.pythonInstallPath": "D:\\tools\\Espidf\\Espressif\\tools\\idf-python\\3.11.2\\python.exe", "files.associations": {"algorithm": "cpp", "mutex": "cpp", "queue": "cpp", "functional": "cpp"}}