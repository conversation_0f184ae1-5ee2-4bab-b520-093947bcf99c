# PlatformIO配置文件 - 语音混合系统版本
# 基于原有配置，添加语音混合系统支持

[env:esp32dev_voice_hybrid]
platform = espressif32
board = esp32dev
framework = arduino

# 使用新的分区表
board_build.partitions = ble_gateway_partition_v2.csv

# 编译标志
build_flags = 
    -DCORE_DEBUG_LEVEL=4
    -DBOARD_HAS_PSRAM
    -mfix-esp32-psram-cache-issue
    -DVOICE_HYBRID_SYSTEM_ENABLED=1
    -DVOICE_ROM_PARTITION_SIZE=0x50000
    -DVOICE_CACHE_SIZE_KB=64
    -DVOICE_COMPRESSION_ENABLED=1

# 库依赖
lib_deps = 
    bblanchon/ArduinoJson@^6.21.3
    https://github.com/schreibfaul1/ESP32-audioI2S
    lorol/LittleFS_esp32@^1.0.6
    WiFi
    HTTPClient
    WiFiClientSecure

# 监控配置
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

# 上传配置
upload_speed = 921600

# 构建前脚本 - 生成语音ROM文件
extra_scripts = 
    pre:src/scripts/build_voice_rom.py

# 自定义构建标志
build_src_filter = 
    +<*>
    +<voice_*.cpp>
    +<scripts/>

# Flash配置
board_build.flash_mode = dio
board_build.f_flash = 80000000L
board_build.f_cpu = 240000000L

# 内存配置
board_build.arduino.memory_type = qio_opi

# 调试配置
debug_tool = esp-prog
debug_init_break = tbreak setup

# 自定义目标
[env:esp32dev_voice_hybrid_debug]
extends = env:esp32dev_voice_hybrid
build_type = debug
build_flags = 
    ${env:esp32dev_voice_hybrid.build_flags}
    -DDEBUG=1
    -DVOICE_DEBUG_ENABLED=1
    -O0
    -g3

[env:esp32dev_voice_hybrid_release]
extends = env:esp32dev_voice_hybrid
build_type = release
build_flags = 
    ${env:esp32dev_voice_hybrid.build_flags}
    -DNDEBUG=1
    -O2
    -DVOICE_OPTIMIZE_SIZE=1
