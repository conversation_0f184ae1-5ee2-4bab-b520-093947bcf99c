name=OneButton
version=2.6.1
author=<PERSON>
maintainer=<PERSON>, https://www.mathertel.de
sentence=Arduino library for improving the usage of a singe input button.
paragraph=It supports detecting events like single, double, multiple clicks and long-time pressing. This enables you to reuse the same button for multiple functions and lowers the hardware invests.
category=Signal Input/Output
url=https://github.com/mathertel/OneButton
architectures=*
includes=OneButton.h
license=BSD-3-Clause
