/**
 * @file voice_integration_example.cpp
 * @brief 语音混合系统集成示例
 *
 * 本文件展示如何在现有的BLE网关项目中集成新的语音混合系统
 */

#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <WiFi.h>
#include <Audio.h>

static const char *TAG = "VoiceIntegration";

// 外部声明（来自原有代码）
extern String wav_file_path[10];
extern uint8_t wav_exist_flag[10];
extern uint8_t audio_enable_flag;
extern Audio audio;

/**
 * @brief 初始化语音混合系统（替换原有的语音初始化）
 */
void init_voice_hybrid_system(void)
{
    ESP_LOGI(TAG, "Initializing voice hybrid system...");

    // 初始化语音混合系统
    voice_error_t err = voice_hybrid_init();
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize voice hybrid system: %d", err);
        return;
    }

    // 设置优先级策略（可根据需要调整）
    voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);

    // 设置缓存大小
    voice_set_cache_size(64); // 64KB缓存

    // 启用压缩
    voice_set_compression_enabled(true);

    // 获取存储状态信息
    voice_storage_info_t storage_info;
    err = voice_get_storage_info(&storage_info);
    if (err == VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "Storage info:");
        ESP_LOGI(TAG, "  ROM: %d/%d bytes, %d files",
                 storage_info.rom_used_size, storage_info.rom_total_size,
                 storage_info.rom_file_count);
        ESP_LOGI(TAG, "  LittleFS: %d/%d bytes, %d files",
                 storage_info.littlefs_used_size, storage_info.littlefs_total_size,
                 storage_info.littlefs_file_count);
        ESP_LOGI(TAG, "  Cache: %d/%d bytes",
                 storage_info.cache_used_size, storage_info.cache_total_size);
    }

    // 预加载常用语音文件
    const char *preload_files[] = {
        "open_app_to_config",
        "network_success",
        "select_user"};

    for (int i = 0; i < 3; i++)
    {
        err = voice_preload(preload_files[i]);
        if (err == VOICE_ERR_OK)
        {
            ESP_LOGD(TAG, "Preloaded: %s", preload_files[i]);
        }
    }

    ESP_LOGI(TAG, "Voice hybrid system initialized successfully");
}

/**
 * @brief 播放语音文件（替换原有的播放函数）
 */
void play_voice_hybrid(const char *voice_name)
{
    if (!voice_name || audio_enable_flag == 0)
    {
        return;
    }

    ESP_LOGI(TAG, "Playing voice: %s", voice_name);

    // 使用智能播放（带自动回退）
    voice_error_t err = voice_play_smart(voice_name);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to play voice %s: %d", voice_name, err);

        // 如果智能播放失败，尝试传统方式作为最后的回退
        ESP_LOGW(TAG, "Falling back to traditional playback...");
        // 这里可以调用原有的播放函数作为最终回退
    }
    else
    {
        ESP_LOGI(TAG, "Voice played successfully: %s", voice_name);
    }
}

/**
 * @brief 检查并更新语音文件
 */
void check_and_update_voice_files(void)
{
    ESP_LOGI(TAG, "Checking for voice file updates...");

    // 检查网络连接状态
    if (WiFi.status() != WL_CONNECTED)
    {
        ESP_LOGW(TAG, "WiFi not connected, skipping update check");
        return;
    }

    // 批量更新所有语音文件
    voice_error_t err = voice_update_all_from_cloud();
    if (err == VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "Voice files update completed successfully");
    }
    else
    {
        ESP_LOGW(TAG, "Voice files update failed: %d", err);
    }

    // 监控存储空间
    voice_monitor_storage();
}

/**
 * @brief 定期维护任务
 */
void voice_maintenance_task(void)
{
    static uint32_t last_maintenance = 0;
    const uint32_t MAINTENANCE_INTERVAL = 3600000; // 1小时

    uint32_t current_time = millis();
    if (current_time - last_maintenance >= MAINTENANCE_INTERVAL)
    {
        ESP_LOGI(TAG, "Running voice system maintenance...");

        // 监控存储空间
        voice_monitor_storage();

        // 获取详细存储统计
        voice_storage_info_t storage_info;
        voice_error_t err = voice_get_storage_stats(&storage_info);
        if (err == VOICE_ERR_OK)
        {
            ESP_LOGI(TAG, "Storage maintenance completed");
        }

        last_maintenance = current_time;
    }
}

/**
 * @brief 处理语音播放请求（兼容原有接口）
 */
void handle_voice_request(int voice_index)
{
    // 映射原有的语音索引到新的语音名称
    const char *voice_names[] = {
        "open_app_to_config",  // 0
        "network_success",     // 1
        "select_user",         // 2
        "tap_smart_config",    // 3
        "blood_pressure_data", // 4
        "temperature_data",    // 5
        "weight_data",         // 6
        "blood_glucose_data",  // 7
        "blood_oxygen_data",   // 8
        "new_message"          // 9
    };

    if (voice_index >= 0 && voice_index < 10)
    {
        play_voice_hybrid(voice_names[voice_index]);
    }
    else
    {
        ESP_LOGE(TAG, "Invalid voice index: %d", voice_index);
    }
}

/**
 * @brief 获取语音文件状态（兼容原有接口）
 */
bool is_voice_available(int voice_index)
{
    const char *voice_names[] = {
        "open_app_to_config", "network_success", "select_user", "tap_smart_config",
        "blood_pressure_data", "temperature_data", "weight_data", "blood_glucose_data",
        "blood_oxygen_data", "new_message"};

    if (voice_index < 0 || voice_index >= 10)
    {
        return false;
    }

    voice_file_info_t file_info;
    voice_error_t err = voice_get_file_info(voice_names[voice_index], &file_info);
    return (err == VOICE_ERR_OK);
}

/**
 * @brief 语音系统状态报告
 */
void report_voice_system_status(void)
{
    ESP_LOGI(TAG, "=== Voice System Status Report ===");

    // 获取存储信息
    voice_storage_info_t storage_info;
    voice_error_t err = voice_get_storage_stats(&storage_info);
    if (err == VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "ROM Storage: %d/%d bytes (%d%%), %d files",
                 storage_info.rom_used_size, storage_info.rom_total_size,
                 (storage_info.rom_used_size * 100) / storage_info.rom_total_size,
                 storage_info.rom_file_count);

        ESP_LOGI(TAG, "LittleFS Storage: %d/%d bytes (%d%%), %d files",
                 storage_info.littlefs_used_size, storage_info.littlefs_total_size,
                 (storage_info.littlefs_used_size * 100) / storage_info.littlefs_total_size,
                 storage_info.littlefs_file_count);

        ESP_LOGI(TAG, "Cache: %d/%d bytes (%d%%)",
                 storage_info.cache_used_size, storage_info.cache_total_size,
                 (storage_info.cache_used_size * 100) / storage_info.cache_total_size);
    }

    // 检查各个语音文件的可用性
    const char *voice_names[] = {
        "open_app_to_config", "network_success", "select_user", "tap_smart_config",
        "blood_pressure_data", "temperature_data", "weight_data", "blood_glucose_data",
        "blood_oxygen_data", "new_message"};

    ESP_LOGI(TAG, "Voice Files Status:");
    for (int i = 0; i < 10; i++)
    {
        voice_file_info_t file_info;
        err = voice_get_file_info(voice_names[i], &file_info);
        if (err == VOICE_ERR_OK)
        {
            const char *source_name[] = {"RAM", "LittleFS", "ROM", "Default", "None"};
            ESP_LOGI(TAG, "  %s: Available (Source: %s, Size: %d, Version: %d)",
                     voice_names[i], source_name[file_info.source],
                     file_info.file_size, file_info.version);
        }
        else
        {
            ESP_LOGW(TAG, "  %s: Not Available", voice_names[i]);
        }
    }

    ESP_LOGI(TAG, "=== End of Status Report ===");
}

/**
 * @brief 语音系统配置更新
 */
void update_voice_system_config(voice_priority_policy_t policy, uint32_t cache_size_kb)
{
    ESP_LOGI(TAG, "Updating voice system configuration...");

    voice_set_priority_policy(policy);
    voice_set_cache_size(cache_size_kb);

    const char *policy_names[] = {"Speed First", "Quality First", "Reliability First"};
    ESP_LOGI(TAG, "Configuration updated: Policy=%s, Cache=%dKB",
             policy_names[policy], cache_size_kb);
}
