---
description: 
globs: 
alwaysApply: true
---
# 安全指南

本项目涉及物联网设备,需要特别注意以下安全事项:

## 网络安全

1. MQTT通信
   - 使用TLS加密通信
   - 实现证书验证
   - 定期更新密钥
   - 避免明文传输敏感信息

2. WiFi连接
   - 支持WPA2/WPA3加密
   - 保护WiFi配置信息
   - 实现安全的配网机制

## 数据安全

1. 本地存储
   - 敏感数据加密存储
   - 定期清理临时数据
   - 实现数据备份机制

2. 用户数据
   - 保护用户隐私信息
   - 实现数据访问控制
   - 提供数据删除功能

## 设备安全

1. 固件更新
   - 实现安全的OTA机制
   - 验证固件签名
   - 保护更新过程

2. 蓝牙安全
   - 实现设备认证机制
   - 保护配对过程
   - 限制扫描范围

## 运行安全

1. 内存保护
   - 防止缓冲区溢出
   - 检查内存泄漏
   - 保护栈空间

2. 异常处理
   - 实现看门狗机制
   - 记录异常日志
   - 支持远程诊断

## 安全审计

1. 日志记录
   - 记录重要操作
   - 保存错误信息
   - 支持远程日志

2. 安全监控
   - 监控异常行为
   - 检测攻击尝试
   - 实现报警机制

