name=ESP32-audioI2S-master
version=2.3.0
author=schreibfaul1
maintainer=schreibfaul1
sentence=With this library You can easily build a WebRadio with a ESP32 board and a I2S-module.
paragraph=Plays webradio, playlists can be m3u, pls or asx. Data format can be only mp3, aac, flac or m4a. It can also play files from a SD Card.
category=Device Control
url=https://github.com/schreibfaul1/ESP32-audioI2S
architectures=esp32
