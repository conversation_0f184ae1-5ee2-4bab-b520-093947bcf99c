{"name": "ESP32-audioI2S", "version": "2.3.0", "description": "With this library You can easily build a WebRadio with a ESP32 board and a I2S-module", "keywords": "audio, i2s, esp32", "repository": {"type": "git", "url": "https://github.com/esphome/ESP32-audioI2S.git"}, "authors": [{"name": "schreibfaul1"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "license": "GPL-3.0", "homepage": "https://github.com/esphome/ESP32-audioI2S", "dependencies": {}, "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": "espressif32"}