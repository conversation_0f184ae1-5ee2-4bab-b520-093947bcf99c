---
description: 
globs: 
alwaysApply: true
---
# 编码规范

本项目采用C++编程语言,需要遵循以下编码规范:

## 命名规范

1. 变量命名:
   - 使用小写字母
   - 多个单词用下划线分隔
   - 示例: `wifi_state`, `ble_scan_time`

2. 常量命名:
   - 全大写字母
   - 多个单词用下划线分隔
   - 示例: `MAX_USER_NUMBER`, `WIFI_TIMEOUT`

3. 函数命名:
   - 使用小写字母
   - 动词_名词形式
   - 示例: `wifi_init()`, `ble_scan_handler()`

4. 结构体命名:
   - 以S_开头
   - 采用大驼峰命名法
   - 示例: `S_BloodOxygen`, `S_WifiInfo`

## 代码格式

1. 缩进使用4个空格
2. 大括号采用K&R风格
3. 每行不超过120个字符
4. 运算符前后添加空格
5. 逗号后面加空格

## 注释规范

1. 文件头部需要注释说明文件功能
2. 重要函数需要添加功能说明注释
3. 复杂逻辑需要添加行内注释
4. 使用英文注释

## 错误处理

1. 所有关键操作需要进行错误检查
2. 使用log_i()等日志函数记录重要信息
3. 对可能的异常情况进行处理

## 资源管理

1. 及时释放动态分配的内存
2. 正确关闭打开的文件
3. 合理使用互斥锁保护共享资源

## 性能优化

1. 避免频繁的动态内存分配
2. 合理使用任务调度
3. 减少不必要的数据拷贝
4. 优化循环结构

